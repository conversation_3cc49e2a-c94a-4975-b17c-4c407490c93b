<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MonoMod.RuntimeDetour</name>
    </assembly>
    <members>
        <member name="T:MonoMod.RuntimeDetour.Detour">
            <summary>
            A fully managed detour.
            Multiple Detours for a method to detour from can exist at any given time. Detours can be layered.
            If you're writing your own detour manager or need to detour native functions, it's better to create instances of NativeDetour instead.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Detour.Apply">
            <summary>
            Mark the detour as applied in the detour chain. This can be done automatically when creating an instance.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Detour.Undo">
            <summary>
            Undo the detour without freeing it, allowing you to reapply it later.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Detour.Free">
            <summary>
            Free the detour, while also permanently undoing it. This makes any further operations on this detour invalid.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Detour.Dispose">
            <summary>
            Undo and free this temporary detour.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Detour.GenerateTrampoline(System.Reflection.MethodBase)">
            <summary>
            Generate a new DynamicMethod with which you can invoke the previous state.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Detour.GenerateTrampoline``1">
            <summary>
            Generate a new DynamicMethod with which you can invoke the previous state.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.Hook.GenerateTrampoline``1">
            <summary>
            Generate a new DynamicMethod with which you can invoke the previous state.
            </summary>
        </member>
        <member name="T:MonoMod.RuntimeDetour.NativeDetour">
            <summary>
            A "raw" native detour, acting as a wrapper around NativeDetourData with a few helpers.
            Only one NativeDetour for a method to detour from can exist at any given time. NativeDetours cannot be layered.
            If you don't need the trampoline generator or any of the management helpers, use DetourManager.Native directly.
            Unless you're writing your own detour manager or need to detour native functions, it's better to create instances of Detour instead.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.Apply">
            <summary>
            Apply the native detour. This can be done automatically when creating an instance.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.Undo">
            <summary>
            Undo the native detour without freeing the detour native data, allowing you to reapply it later.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.ChangeSource(System.IntPtr)">
            <summary>
            Changes the source of this native detour to a new source address. This does not repair the old source location.
            This also assumes that <paramref name="newSource"/> is simply a new address for the same method as this was constructed with.
            </summary>
            <param name="newSource">The new source location.</param>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.ChangeTarget(System.IntPtr)">
            <summary>
            Changed the target of this native detour to a new target.
            </summary>
            <param name="newTarget">The new target address.</param>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.Free">
            <summary>
            Free the detour's data without undoing it. This makes any further operations on this detour invalid.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.Dispose">
            <summary>
            Undo and free this temporary detour.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.GenerateTrampoline(System.Reflection.MethodBase)">
            <summary>
            Generate a new DynamicMethod with which you can invoke the previous state.
            If the NativeDetour holds a reference to a managed method, a copy of the original method is returned.
            If the NativeDetour holds a reference to a native function, an "undo-call-redo" trampoline with a matching signature is returned.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.NativeDetour.GenerateTrampoline``1">
            <summary>
            Generate a new delegate with which you can invoke the previous state.
            If the NativeDetour holds a reference to a managed method, a copy of the original method is returned.
            If the NativeDetour holds a reference to a native function, an "undo-call-redo" trampoline with a matching signature is returned.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.Write(System.IntPtr,System.Int32@,System.Byte)">
            <summary>
            Write the given value at the address to + offs, afterwards advancing offs by sizeof(byte).
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.Write(System.IntPtr,System.Int32@,System.UInt16)">
            <summary>
            Write the given value at the address to + offs, afterwards advancing offs by sizeof(ushort).
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.Write(System.IntPtr,System.Int32@,System.UInt32)">
            <summary>
            Write the given value at the address to + offs, afterwards advancing offs by sizeof(ushort).
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.Write(System.IntPtr,System.Int32@,System.UInt64)">
            <summary>
            Write the given value at the address to + offs, afterwards advancing offs by sizeof(ulong).
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.GenerateNativeProxy(System.IntPtr,System.Reflection.MethodBase)">
            <summary>
            Generate a DynamicMethod to easily call the given native function from another DynamicMethod.
            </summary>
            <param name="target">The pointer to the native function to call.</param>
            <param name="signature">A MethodBase with the target function's signature.</param>
            <returns>The detoured DynamicMethod.</returns>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.StubCriticalDetour(MonoMod.Utils.DynamicMethodDefinition)">
            <summary>
            Fill the DynamicMethodDefinition with a throw.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.EmitDetourCopy(Mono.Cecil.Cil.ILProcessor,System.IntPtr,System.IntPtr,System.Byte)">
            <summary>
            Emit a call to DetourManager.Native.Copy using the given parameters.
            </summary>
        </member>
        <member name="M:MonoMod.RuntimeDetour.DetourHelper.EmitDetourApply(Mono.Cecil.Cil.ILProcessor,MonoMod.RuntimeDetour.NativeDetourData)">
            <summary>
            Emit a call to DetourManager.Native.Apply using a copy of the given data.
            </summary>
        </member>
        <member name="T:MonoMod.RuntimeDetour.NativeDetourData">
            <summary>
            The data forming a "raw" native detour, created and consumed by DetourManager.Native.
            </summary>
        </member>
        <member name="F:MonoMod.RuntimeDetour.NativeDetourData.Method">
            <summary>
            The method to detour from. Set when the structure is created by the IDetourNativePlatform.
            </summary>
        </member>
        <member name="F:MonoMod.RuntimeDetour.NativeDetourData.Target">
            <summary>
            The target method to be called instead. Set when the structure is created by the IDetourNativePlatform.
            </summary>
        </member>
        <member name="F:MonoMod.RuntimeDetour.NativeDetourData.Type">
            <summary>
            The type of the detour. Determined when the structure is created by the IDetourNativePlatform.
            </summary>
        </member>
        <member name="F:MonoMod.RuntimeDetour.NativeDetourData.Size">
            <summary>
            The size of the detour. Calculated when the structure is created by the IDetourNativePlatform.
            </summary>
        </member>
        <member name="F:MonoMod.RuntimeDetour.NativeDetourData.Extra">
            <summary>
            DetourManager.Native-specific data.
            </summary>
        </member>
    </members>
</doc>
