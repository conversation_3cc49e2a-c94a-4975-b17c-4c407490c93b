<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SlugBase</name>
    </assembly>
    <members>
        <member name="T:SlugBase.Assets.CustomDreams">
            <summary>
            Helpers for adding custom dreams.
            </summary>
        </member>
        <member name="M:SlugBase.Assets.CustomDreams.SetDreamScene(DreamsState.DreamID,Menu.MenuScene.SceneID)">
            <summary>
            Registers a new dream and its associated scene.
            </summary>
            <param name="dream">The dream ID to add.</param>
            <param name="scene">The scene ID to display when the dream occurs.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dream"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:SlugBase.Assets.CustomDreams.QueueDream(StoryGameSession,DreamsState.DreamID)">
            <summary>
            Set the dream scene that will display when the player hibernates next.
            </summary>
            <param name="storySession">The current story game session.</param>
            <param name="dreamID">The id of the dream to queue.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dreamID"/> or <paramref name="storySession"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="dreamID"/> wasn't registered with <see cref="M:SlugBase.Assets.CustomDreams.SetDreamScene(DreamsState.DreamID,Menu.MenuScene.SceneID)"/>.</exception>
        </member>
        <member name="T:SlugBase.Assets.CustomScene">
            <summary>
            A scene added by SlugBase.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Registry">
            <summary>
            Stores all registered <see cref="T:SlugBase.Assets.CustomScene"/>s.
            </summary>
        </member>
        <member name="M:SlugBase.Assets.CustomScene.SetSelectMenuScene(SaveState,Menu.MenuScene.SceneID)">
            <summary>
            Set or unset the select menu scene override for a save state.
            </summary>
            <param name="save">The save state to modify.</param>
            <param name="id">The new select menu scene ID, or <see langword="null"/> to remove the override.</param>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.ID">
            <summary>
            This scene's unique ID.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Images">
            <summary>
            An array of images in this scene.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.IdleDepths">
            <summary>
            An array of depths that the camera may focus on.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.SceneFolder">
            <summary>
            A path relative to StreamingAssets to load images from.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.GlowPos">
            <summary>
            The position of the glow's center.
            <para>Only effective when used on the slugcat select screen.</para>
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.MarkPos">
            <summary>
            The position of the mark's center.
            <para>Only effective when used on the slugcat select screen.</para>
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.SelectMenuOffset">
            <summary>
            The pixel offset for this scene in the select menu.
            <para>Only effective when used on the slugcat select screen.</para>
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.SlugcatDepth">
            <summary>
            The depth of the slugcat image in this scene.
            <para>Only effective when used on the slugcat select screen.</para>
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.OverrideDream">
            <summary>
            If a scene is used as a dream, should it replace any current dream.
            </summary>
        </member>
        <member name="T:SlugBase.Assets.CustomScene.Image">
            <summary>
            An image from a <see cref="T:SlugBase.Assets.CustomScene"/>.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Image.Name">
            <summary>
            The file name of the image to load. This is combined with <see cref="P:SlugBase.Assets.CustomScene.SceneFolder"/>.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Image.Position">
            <summary>
            The pixel position of this image's bottom left corner in the scene.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Image.Depth">
            <summary>
            The depth of this image in the scene.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Image.Shader">
            <summary>
            The shader to use when rendering. Defaults to <see cref="F:Menu.MenuDepthIllustration.MenuShader.Normal"/>.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomScene.Image.Flatmode">
            <summary>
            If <c>true</c>, this image will display when in flat mode and will be hidden otherwise.
            </summary>
        </member>
        <member name="M:SlugBase.Assets.CustomScene.Image.#ctor(System.String,UnityEngine.Vector2)">
            <summary>
            Creates a new image.
            </summary>
            <param name="name">The file name.</param>
            <param name="position">The pixel position of the bottom left corner.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:SlugBase.Assets.CustomScene.Image.#ctor(SlugBase.JsonObject)">
            <summary>
            Creates a new image from JSON.
            </summary>
            <param name="json">The JSON data to load from.</param>
        </member>
        <member name="T:SlugBase.Assets.CustomSlideshow">
            <summary>
            An intro cutscene added by SlugBase.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.Registry">
            <summary>
            Stores all registered <see cref="T:SlugBase.Assets.CustomSlideshow"/>s.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.ID">
            <summary>
            This scene's unique ID.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.SlideshowFolder">
            <summary>
            A path relative to StreamingAssets to load images from.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.Music">
            <summary>
            The music to play during a custom intro or outro
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.Scenes">
            <summary>
            An array of images and other data in this scene.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.Process">
            <summary>
            The process to go to after playing the slideshow
            </summary>
        </member>
        <member name="T:SlugBase.Assets.CustomSlideshow.CustomSlideshowScene">
            <summary>
            A scene from a <see cref="T:SlugBase.Assets.CustomSlideshow"/> that holds data about when to appear and what images to use for what amount of time.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.CustomSlideshowScene.StartAt">
            <summary>
            The time that this scene will start fading in, in seconds.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.CustomSlideshowScene.FadeInDoneAt">
            <summary>
            The time that this image will finish fading in, in seconds.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.CustomSlideshowScene.FadeOutStartAt">
            <summary>
            The time that this image will start fading out at, in seconds.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.CustomSlideshowScene.CameraMovement">
            <summary>
            The positions that the camera will focus on when playing this scene.
            </summary>
            <remarks>
            X and Y represent the pixel position of the camera, while Z represents the focus depth.
            </remarks>
        </member>
        <member name="M:SlugBase.Assets.CustomSlideshow.CustomSlideshowScene.#ctor(SlugBase.JsonObject)">
            <summary>
            Creates a new Scene from JSON.
            </summary>
            <param name="json">The JSON data to load from.</param>
        </member>
        <member name="T:SlugBase.Assets.CustomSlideshow.SlideshowMusic">
            <summary>
            Data about a song from a <see cref="T:SlugBase.Assets.CustomSlideshow"/>.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.SlideshowMusic.Name">
            <summary>
            The file name of the sound to use. This comes from the `StreamingAssets/music/songs` folder.
            </summary>
        </member>
        <member name="P:SlugBase.Assets.CustomSlideshow.SlideshowMusic.FadeIn">
            <summary>
            The duration of the song's fade in, in seconds.
            </summary>
        </member>
        <member name="M:SlugBase.Assets.CustomSlideshow.SlideshowMusic.#ctor(SlugBase.JsonObject)">
            <summary>
            Load information about a slideshow song from JSON data.
            </summary>
            <param name="json">The JSON data to load from.</param>
        </member>
        <member name="T:SlugBase.CustomTimeline">
            <summary>
            A timeline added by SlugBase.
            </summary>
        </member>
        <member name="P:SlugBase.CustomTimeline.Registry">
            <summary>
            Stores all registered <see cref="T:SlugBase.CustomTimeline"/>s.
            </summary>
        </member>
        <member name="P:SlugBase.CustomTimeline.OrderedTimelines">
            <summary>
            All registered <see cref="T:SlugBase.CustomTimeline"/>s to be added to <see cref="M:SlugcatStats.SlugcatTimelineOrder"/>.
            </summary>
            <remarks>
            This list is topologically sorted. Each timeline may reference those ahead of it in <see cref="P:SlugBase.CustomTimeline.InsertBefore"/> or <see cref="P:SlugBase.CustomTimeline.InsertAfter"/>.
            </remarks>
        </member>
        <member name="M:SlugBase.CustomTimeline.ReloadTimelineOrder">
            <summary>
            Reload the order of custom timelines text time <see cref="P:SlugBase.CustomTimeline.OrderedTimelines"/> is accessed.
            </summary>
        </member>
        <member name="P:SlugBase.CustomTimeline.ID">
            <summary>
            This timeline's unique ID.
            </summary>
        </member>
        <member name="P:SlugBase.CustomTimeline.Base">
            <summary>
            An array of timelines this timeline inherits from.
            </summary>
            <remarks>
            If a file specific to this timeline isn't found, these are checked in order before using the default.
            </remarks>
        </member>
        <member name="P:SlugBase.CustomTimeline.InsertBefore">
            <summary>
            The timeline that comes after this.
            </summary>
            <remarks>
            When determining the order of timelines, this one will be inserted immediately before
            the first registered element in <see cref="P:SlugBase.CustomTimeline.InsertBefore"/>.
            </remarks>
            <seealso cref="P:SlugBase.CustomTimeline.InsertAfter"/>
        </member>
        <member name="P:SlugBase.CustomTimeline.InsertAfter">
            <summary>
            The timeline that comes before this.
            </summary>
            <remarks>
            When determining the order of timelines, this one will be inserted immediately after
            the first registered element in <see cref="P:SlugBase.CustomTimeline.InsertBefore"/>.
            </remarks>
            <seealso cref="P:SlugBase.CustomTimeline.InsertAfter"/>
        </member>
        <member name="P:SlugBase.CustomTimeline.Priorities">
            <summary>
            All valid options to use for timeline inheritance, starting with <see cref="P:SlugBase.CustomTimeline.ID"/> and continuing through <see cref="P:SlugBase.CustomTimeline.Base"/>.
            This includes parent timelines from <see cref="T:SlugBase.CustomTimeline"/>s this inherits from.
            </summary>
        </member>
        <member name="M:SlugBase.CustomTimeline.AddFlattenedPriorities(System.Collections.Generic.List{SlugcatStats.Timeline})">
            <summary>
            List all timelines this inherits from. Unlike <see cref="P:SlugBase.CustomTimeline.Priorities"/>,
            this includes <see cref="P:SlugBase.CustomTimeline.Base"/> timelines from parents.
            </summary>
        </member>
        <member name="M:SlugBase.CustomTimeline.InheritsFrom(SlugcatStats.Timeline)">
            <summary>
            Check if <paramref name="parent"/> is in the list of base timelines.
            </summary>
        </member>
        <member name="M:SlugBase.CustomTimeline.GetTimelines(SlugBase.JsonAny)">
            <summary>
            Converts a string or list of strings to a list of timelines.
            </summary>
        </member>
        <member name="M:SlugBase.CustomTimeline.GetTimeline(System.String)">
            <summary>
            Converts a string to a timeline, ignoring case.
            </summary>
        </member>
        <member name="T:SlugBase.DataTypes.ColorSlot">
            <summary>
            Represents a color that may be configured by the user.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.ColorSlot.Index">
            <summary>
            The index of this color slot in <see cref="M:PlayerGraphics.ColoredBodyPartList(SlugcatStats.Name)"/>.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.ColorSlot.Name">
            <summary>
            This color's name for use with <see cref="T:SlugBase.DataTypes.PlayerColor"/>.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.ColorSlot.Default">
            <summary>
            The default color.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.ColorSlot.Variants">
            <summary>
            The preset colors to use in multiplayer.
            </summary>
        </member>
        <member name="M:SlugBase.DataTypes.ColorSlot.#ctor(System.Int32,System.String)">
            <summary>
            Create an empty <see cref="T:SlugBase.DataTypes.ColorSlot"/>.
            </summary>
            <param name="index">The index for use with <see cref="M:PlayerGraphics.ColoredBodyPartList(SlugcatStats.Name)"/>.</param>
            <param name="name">The name of the body part this colors.</param>
        </member>
        <member name="M:SlugBase.DataTypes.ColorSlot.#ctor(System.Int32,SlugBase.JsonAny)">
            <summary>
            Create a <see cref="T:SlugBase.DataTypes.ColorSlot"/> from JSON.
            </summary>
            <param name="index">The index for use with <see cref="M:PlayerGraphics.ColoredBodyPartList(SlugcatStats.Name)"/>.</param>
            <param name="json">The JSON to load.</param>
        </member>
        <member name="M:SlugBase.DataTypes.ColorSlot.GetColor(System.Int32)">
            <summary>
            Gets a color variant from <see cref="P:SlugBase.DataTypes.ColorSlot.Variants"/> by index.
            </summary>
            <param name="slugcatCharacter">The index.</param>
            <returns>The color for <paramref name="slugcatCharacter"/> from <see cref="P:SlugBase.DataTypes.ColorSlot.Variants"/>, or <see cref="P:SlugBase.DataTypes.ColorSlot.Default"/> if the index was out of range.</returns>
        </member>
        <member name="M:SlugBase.DataTypes.ColorSlot.GetColor(PlayerGraphics)">
            <summary>
            Gets the color of this slot for a given player.
            </summary>
            <param name="graphics">The player graphics to get the color from.</param>
            <returns>The color of this body part after modifications are applied.</returns>
        </member>
        <member name="T:SlugBase.DataTypes.Diet">
            <summary>
            Represents the nourishment and edibility of foods for a <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.Diet.Corpses">
            <summary>
            The food value multiplier of dead creatures.
            <para>If the creature is consumed in its entirety, such as <see cref="F:CreatureTemplate.Type.Fly"/>, <see cref="P:SlugBase.DataTypes.Diet.Meat"/> is used instead.</para>
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.Diet.Meat">
            <summary>
            The food value multiplier of small creatures or living objects like <see cref="F:AbstractPhysicalObject.AbstractObjectType.JellyFish"/>.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.Diet.Plants">
            <summary>
            The food value multiplier of non-living foods.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.Diet.ObjectOverrides">
            <summary>
            The food value multipliers of individual object types.
            </summary>
        </member>
        <member name="P:SlugBase.DataTypes.Diet.CreatureOverrides">
            <summary>
            The food value multipliers of individual creature types.
            </summary>
        </member>
        <member name="M:SlugBase.DataTypes.Diet.#ctor(SlugBase.JsonAny)">
            <summary>
            Creates a new <see cref="T:SlugBase.DataTypes.Diet"/> from JSON.
            </summary>
            <param name="json">The JSON to load.</param>
        </member>
        <member name="M:SlugBase.DataTypes.Diet.GetFoodMultiplier(PhysicalObject)">
            <summary>
            Gets the food value multiplier for an object that was consumed in its entirety.
            </summary>
            <param name="obj">The object that was eaten.</param>
            <returns>A multiplier for the nourishment of this object.</returns>
        </member>
        <member name="M:SlugBase.DataTypes.Diet.GetMeatMultiplier(Player,Creature)">
            <summary>
            Gets the food value multiplier for a creature
            </summary>
            <param name="player">The player to pass to <see cref="M:Player.EatMeatOmnivoreGreenList(Creature)"/>.</param>
            <param name="crit">The creature that is being eaten.</param>
            <returns>A multiplier for the nourishment of this creature.</returns>
        </member>
        <member name="T:SlugBase.DataTypes.PlayerColor">
            <summary>
            Represents a color that may copy from <see cref="F:SlugBase.Features.PlayerFeatures.CustomColors"/>.
            </summary>
        </member>
        <member name="F:SlugBase.DataTypes.PlayerColor.Body">
            <summary>
            The body color of <see cref="T:SlugBase.SlugBaseCharacter"/>s.
            </summary>
        </member>
        <member name="F:SlugBase.DataTypes.PlayerColor.Eyes">
            <summary>
            The eye color of <see cref="T:SlugBase.SlugBaseCharacter"/>s.
            </summary>
        </member>
        <member name="M:SlugBase.DataTypes.PlayerColor.#ctor(SlugBase.JsonAny)">
            <summary>
            Creates a new <see cref="T:SlugBase.DataTypes.PlayerColor"/> from JSON.
            </summary>
            <param name="json">The JSON to load.</param>
        </member>
        <member name="M:SlugBase.DataTypes.PlayerColor.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:SlugBase.DataTypes.PlayerColor"/>.
            </summary>
            <param name="name">The name of the custom color to copy.</param>
        </member>
        <member name="M:SlugBase.DataTypes.PlayerColor.#ctor(UnityEngine.Color)">
            <summary>
            Creates a new <see cref="T:SlugBase.DataTypes.PlayerColor"/>.
            </summary>
            <param name="color">The color to return.</param>
        </member>
        <member name="M:SlugBase.DataTypes.PlayerColor.GetColor(PlayerGraphics)">
            <summary>
            Gets the value of this color for a player.
            </summary>
            <param name="playerGraphics">The player to get the color from.</param>
            <returns>The custom color, or <c>null</c> if it was not overridden via <see cref="F:SlugBase.Features.PlayerFeatures.CustomColors"/>.</returns>
        </member>
        <member name="M:SlugBase.DataTypes.PlayerColor.GetCustomColor(PlayerGraphics,System.String)">
            <summary>
            Gets a color from the "custom_colors" feature by name for the given SlugBase character graphics.
            </summary>
            <param name="playerGraphics">The player graphics to get the color of.</param>
            <param name="name">The name of the custom color.</param>
            <exception cref="T:System.ArgumentException"><paramref name="playerGraphics"/> is not a <see cref="T:SlugBase.SlugBaseCharacter"/> instance, or does not have the "custom_colors" feature.</exception>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">No custom color was found matching <paramref name="name"/>.</exception>
        </member>
        <member name="M:SlugBase.DataTypes.PlayerColor.GetCustomColor(PlayerGraphics,System.Int32)">
            <summary>
            Gets a color from the "custom_colors" feature by index for the given SlugBase character graphics.
            </summary>
            <param name="playerGraphics">The player graphics to get the color of.</param>
            <param name="index">The index of the custom color.</param>
            <exception cref="T:System.ArgumentException"><paramref name="playerGraphics"/> is not a <see cref="T:SlugBase.SlugBaseCharacter"/> instance, or does not have the "custom_colors" feature.</exception>
            <exception cref="T:System.IndexOutOfRangeException"><paramref name="index"/> is out of range of the "custom_colors" list.</exception>
        </member>
        <member name="T:SlugBase.DataTypes.RepOverride">
            <summary>
            Represents the initial reputation of the player with a community.
            </summary>
        </member>
        <member name="F:SlugBase.DataTypes.RepOverride.Target">
            <summary>
            The target like value of the player.
            </summary>
        </member>
        <member name="F:SlugBase.DataTypes.RepOverride.Strength">
            <summary>
            The amount to lerp reputation towards <see cref="F:SlugBase.DataTypes.RepOverride.Target"/> when loaded.
            </summary>
        </member>
        <member name="F:SlugBase.DataTypes.RepOverride.Locked">
            <summary>
            If <c>true</c>, the like of the player will be locked to <see cref="F:SlugBase.DataTypes.RepOverride.Target"/> after it is set.
            </summary>
        </member>
        <member name="M:SlugBase.DataTypes.RepOverride.#ctor(System.Single,System.Single,System.Boolean)">
            <summary>
            Creates a new <see cref="T:SlugBase.DataTypes.RepOverride"/>.
            </summary>
            <param name="target">The target like value of the player.</param>
            <param name="strength">The amount to lerp reputation towards <paramref name="target"/> when loaded.</param>
            <param name="locked">If <c>true</c>, the like of the player will be locked to <paramref name="target"/> after it is set.</param>
        </member>
        <member name="M:SlugBase.DataTypes.RepOverride.#ctor(SlugBase.JsonAny)">
            <summary>
            Creates a new <see cref="T:SlugBase.DataTypes.RepOverride"/> from JSON.
            </summary>
            <param name="json">The JSON to load.</param>
        </member>
        <member name="T:SlugBase.Features.PlayerData`1">
            <summary>
            Stores per-player variables.
            </summary>
            <typeparam name="TValue">The type of data stored.</typeparam>
        </member>
        <member name="M:SlugBase.Features.PlayerData`1.#ctor(SlugBase.Features.Feature)">
            <summary>
            Creates a new per-player variable that depends on <paramref name="requiredFeature"/>.
            </summary>
            <param name="requiredFeature">The required <see cref="T:SlugBase.Features.Feature"/>, or <c>null</c> if data access should not be locked behind a feature.</param>
        </member>
        <member name="M:SlugBase.Features.PlayerData`1.Get(PlayerState)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="state"/>, constructing it if it does not exist.
            <para>If the game's <see cref="T:SlugBase.SlugBaseCharacter"/> does not have <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, then <c>null</c> is returned.</para>
            </summary>
            <param name="state">The player state the variable is associated with.</param>
        </member>
        <member name="M:SlugBase.Features.PlayerData`1.Get(Player)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="player"/>, constructing it if it does not exist.
            <para>If the game's <see cref="T:SlugBase.SlugBaseCharacter"/> does not have <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, then <c>null</c> is returned.</para>
            </summary>
            <param name="player">The player the variable is associated with.</param>
        </member>
        <member name="M:SlugBase.Features.PlayerData`1.TryGet(PlayerState,`0@)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="state"/>, constructing it if it does not exist.
            </summary>
            <param name="state">The player state the variable is associated with.</param>
            <param name="value">The stored value, or <typeparamref name="TValue"/>'s default value if the required feature wasn't found.</param>
            <returns><c>true</c> if the player's <see cref="T:SlugBase.SlugBaseCharacter"/> had <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.Features.PlayerData`1.TryGet(Player,`0@)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="player"/>, constructing it if it does not exist.
            </summary>
            <param name="player">The player the variable is associated with.</param>
            <param name="value">The stored value, or <typeparamref name="TValue"/>'s default value if the required feature wasn't found.</param>
            <returns><c>true</c> if the player's <see cref="T:SlugBase.SlugBaseCharacter"/> had <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:SlugBase.Features.GameData`1">
            <summary>
            Stores per-game variables.
            </summary>
            <typeparam name="TValue">The type of data stored.</typeparam>
        </member>
        <member name="M:SlugBase.Features.GameData`1.#ctor(SlugBase.Features.Feature)">
            <summary>
            Create a new per-game variable that depends on <paramref name="requiredFeature"/>.
            </summary>
            <param name="requiredFeature">The required <see cref="T:SlugBase.Features.Feature"/>, or <c>null</c> if data access should not be locked behind a feature.</param>
        </member>
        <member name="M:SlugBase.Features.GameData`1.Get(RainWorldGame)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="game"/>, constructing it if it does not exist.
            <para>If the game's <see cref="T:SlugBase.SlugBaseCharacter"/> does not have <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, then <c>null</c> is returned.</para>
            </summary>
            <param name="game">The current game.</param>
        </member>
        <member name="M:SlugBase.Features.GameData`1.TryGet(RainWorldGame,`0@)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="game"/>, constructing it if it does not exist.
            </summary>
            <param name="game">The current game.</param>
            <param name="value">The stored value, or <typeparamref name="TValue"/>'s default value if the required feature wasn't found.</param>
            <returns><c>true</c> if <paramref name="game"/> had <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:SlugBase.Features.Data`2">
            <summary>
            Stores <typeparamref name="TValue"/>s associated with <typeparamref name="THolder"/>s.
            </summary>
            <remarks>
            <see cref="T:SlugBase.Features.PlayerData`1"/> and <see cref="T:SlugBase.Features.GameData`1"/> should be used when possible.
            Otherwise, consider making a child class with <c>Get</c> and <c>TryGet</c> methods that find the most
            appropriate <see cref="T:SlugBase.SlugBaseCharacter"/> to pass to <see cref="M:SlugBase.Features.Data`2.Get(SlugBase.SlugBaseCharacter,`0)"/>.
            </remarks>
            <typeparam name="THolder">The key type that values are associated with.</typeparam>
            <typeparam name="TValue">The type of data stored.</typeparam>
        </member>
        <member name="M:SlugBase.Features.Data`2.#ctor(SlugBase.Features.Feature)">
            <summary>
            Creates a new instance of <see cref="T:SlugBase.Features.Data`2"/> that depends on <paramref name="requiredFeature"/>.
            </summary>
            <param name="requiredFeature">The required <see cref="T:SlugBase.Features.Feature"/>, or <c>null</c> if data access should not be locked behind a feature.</param>
        </member>
        <member name="M:SlugBase.Features.Data`2.Get(SlugBase.SlugBaseCharacter,`0)">
            <summary>
            Gets the <typeparamref name="TValue"/> instance assocated with <paramref name="key"/>, constructing it if it does not exist.
            <para>If <paramref name="character"/> does not have <see cref="P:SlugBase.Features.Data.RequiredFeature"/>, then <c>null</c> is returned.</para>
            </summary>
            <param name="character">The <see cref="T:SlugBase.SlugBaseCharacter"/> that may own <see cref="P:SlugBase.Features.Data.RequiredFeature"/>.</param>
            <param name="key">The key the data is attached to.</param>
        </member>
        <member name="T:SlugBase.Features.Data">
            <summary>
            Represents variable information of a <see cref="T:SlugBase.SlugBaseCharacter"/> that depends on a <see cref="T:SlugBase.Features.Feature"/>.
            </summary>
        </member>
        <member name="P:SlugBase.Features.Data.RequiredFeature">
            <summary>
            The feature this data depends upon.
            </summary>
        </member>
        <member name="M:SlugBase.Features.Data.#ctor(SlugBase.Features.Feature)">
            <summary>
            Create a new <see cref="T:SlugBase.Features.Data"/> instance that requires a given feature.
            </summary>
            <param name="requiredFeature">The feature that this requires, or null to not require a feature.</param>
        </member>
        <member name="M:SlugBase.Features.Data.TryUnbox``1(System.Runtime.CompilerServices.StrongBox{``0},``0@)">
            <summary>
            Gets the value of a <see cref="T:System.Runtime.CompilerServices.StrongBox`1"/>.
            </summary>
            <typeparam name="T">The stored value's type.</typeparam>
            <param name="box">The <see cref="T:System.Runtime.CompilerServices.StrongBox`1"/> holding the value or <c>null</c>.</param>
            <param name="value">The stored value, or <c>default</c> if <paramref name="box"/> is <c>null</c>.</param>
            <returns><c>false</c> if <paramref name="box"/> was <c>null</c>, <c>true</c> otherwise.</returns>
        </member>
        <member name="T:SlugBase.Features.PlayerFeature`1">
            <summary>
            A constant setting of a <see cref="T:SlugBase.SlugBaseCharacter"/>'s player.
            </summary>
            <typeparam name="T">The type that stores this setting's information</typeparam>
        </member>
        <member name="M:SlugBase.Features.PlayerFeature`1.#ctor(System.String,System.Func{SlugBase.JsonAny,`0})">
            <summary>
            Creates a new <see cref="T:SlugBase.Features.PlayerFeature`1"/> with the given <paramref name="id"/>.
            </summary>
            <param name="id">The JSON key.</param>
            <param name="factory">A delegate that parses <see cref="T:SlugBase.JsonAny"/> into <typeparamref name="T"/>. An exception should be thrown on failure.</param>
        </member>
        <member name="M:SlugBase.Features.PlayerFeature`1.TryGet(Player,`0@)">
            <summary>
            Gets the <typeparamref name="T"/> instance assocated with <paramref name="player"/>.
            </summary>
            <param name="player">A <see cref="T:Player"/> instance that may be a <see cref="T:SlugBase.SlugBaseCharacter"/> with this <see cref="T:SlugBase.Features.Feature"/>.</param>
            <param name="value">The stored setting, or <typeparamref name="T"/>'s default value if the feature wasn't found.</param>
            <returns><c>true</c> if the <paramref name="player"/>'s <see cref="T:SlugBase.SlugBaseCharacter"/> had this feature, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:SlugBase.Features.GameFeature`1">
            <summary>
            A constant setting of a <see cref="T:SlugBase.SlugBaseCharacter"/>'s save slot.
            </summary>
            <typeparam name="T">The type that stores this setting's information.</typeparam>
        </member>
        <member name="M:SlugBase.Features.GameFeature`1.#ctor(System.String,System.Func{SlugBase.JsonAny,`0})">
            <summary>
            Creates a new <see cref="T:SlugBase.Features.GameFeature`1"/> with the given <paramref name="id"/>.
            </summary>
            <param name="id">The JSON key.</param>
            <param name="factory">A delegate that parses <see cref="T:SlugBase.JsonAny"/> into <typeparamref name="T"/>. An exception should be thrown on failure.</param>
        </member>
        <member name="M:SlugBase.Features.GameFeature`1.TryGet(RainWorldGame,`0@)">
            <summary>
            Gets the <typeparamref name="T"/> instance assocated with <paramref name="game"/>.
            </summary>
            <param name="game">A <see cref="T:RainWorldGame"/> instance that may belong to a <see cref="T:SlugBase.SlugBaseCharacter"/> with this <see cref="T:SlugBase.Features.Feature"/>.</param>
            <param name="value">The stored setting, or <typeparamref name="T"/>'s default value if the feature wasn't found.</param>
            <returns><c>true</c> if the <paramref name="game"/>'s <see cref="T:SlugBase.SlugBaseCharacter"/> had this feature, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:SlugBase.Features.Feature`1">
            <summary>
            A strongly-typed constant setting of a <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
            <remarks>
            <see cref="T:SlugBase.Features.PlayerFeature`1"/> and <see cref="T:SlugBase.Features.GameFeature`1"/> should be used when possible.
            Otherwise, consider making a child class with a <c>TryGet</c> method that finds the most
            appropriate <see cref="T:SlugBase.SlugBaseCharacter"/> to pass to <see cref="M:SlugBase.Features.Feature`1.TryGet(SlugBase.SlugBaseCharacter,`0@)"/>.
            </remarks>
            <typeparam name="T">The type that stores this setting's information.</typeparam>
        </member>
        <member name="M:SlugBase.Features.Feature`1.#ctor(System.String,System.Func{SlugBase.JsonAny,`0})">
            <summary>
            Creates a new <see cref="T:SlugBase.Features.Feature`1"/> with the given <paramref name="id"/>.
            </summary>
            <param name="id">The JSON key.</param>
            <param name="factory">A delegate that parses <see cref="T:SlugBase.JsonAny"/> into <typeparamref name="T"/>. An exception should be thrown on failure.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="factory"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="id"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException">A <see cref="T:SlugBase.Features.Feature"/> with the given <paramref name="id"/> already exists.</exception>
        </member>
        <member name="M:SlugBase.Features.Feature`1.TryGet(SlugBase.SlugBaseCharacter,`0@)">
            <summary>
            Gets the <typeparamref name="T"/> instance assocated with <paramref name="character"/>.
            </summary>
            <param name="character">The <see cref="T:SlugBase.SlugBaseCharacter"/> that may have this <see cref="T:SlugBase.Features.Feature"/>.</param>
            <param name="value">The stored setting, or <typeparamref name="T"/>'s default value if the feature wasn't found.</param>
            <returns><c>true</c> if <paramref name="character"/> had this feature, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:SlugBase.Features.Feature">
            <summary>
            Represents a constant setting of a <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
        </member>
        <member name="P:SlugBase.Features.Feature.ID">
            <summary>
            This <see cref="T:SlugBase.Features.Feature"/>'s JSON key.
            </summary>
        </member>
        <member name="M:SlugBase.Features.Feature.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:SlugBase.Features.Feature"/> with the given <paramref name="id"/>.
            </summary>
            <param name="id">The JSON key.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="id"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException">A <see cref="T:SlugBase.Features.Feature"/> with the given <paramref name="id"/> already exists.</exception>
        </member>
        <member name="M:SlugBase.Features.FeatureHooks.PlayerGraphics_ApplyPalette(MonoMod.Cil.ILContext)">
            CustomColors: Apply body color override
        </member>
        <member name="T:SlugBase.Features.PlayerFeatures">
            <summary>
            Built-in <see cref="T:SlugBase.Features.Feature"/>s describing the player.
            </summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.SlugcatColor">
            <summary>"color": Player body and UI color.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.AutoGrabFlies">
            <summary>"auto_grab_batflies": Grab batflies on collision.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.WeightMul">
            <summary>"weight": Weight multiplier.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.TunnelSpeedMul">
            <summary>"tunnel_speed": Move speed in tunnels.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.ClimbSpeedMul">
            <summary>"climb_speed": Move speed on poles.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.WalkSpeedMul">
            <summary>"walk_speed": Standing move speed.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.CrouchStealth">
            <summary>"crouch_stealth": Visual stealth while crouched.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.ThrowSkill">
            <summary>"throw_skill": Spear damage and speed.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.LungsCapacityMul">
            <summary>"lung_capacity": Time underwater before drowning.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.LoudnessMul">
            <summary>"loudness": Sound alert multiplier.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.BackSpear">
            <summary>"back_spear": Store a spear on back.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.CommunityAlignments">
            <summary>"alignments": Initial community reputation.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.Diet">
            <summary>"diet": Edibility and nourishment of foods.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.CustomColors">
            <summary>"custom_colors": Configurable player colors.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.CanMaul">
            <summary>"can_maul": Ability to maul creatures.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.MaulDamage">
            <summary>"maul_damage": Damage of maul attack.</summary>
        </member>
        <member name="F:SlugBase.Features.PlayerFeatures.MaulBlacklist">
            <summary>"maul_blacklist": Creatures that cannot be mauled.</summary>
        </member>
        <member name="T:SlugBase.Features.GameFeatures">
            <summary>
            Built-in <see cref="T:SlugBase.Features.Feature"/>s describing the game.
            </summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.Karma">
            <summary>"karma": Initial karma.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.KarmaCap">
            <summary>"karma_cap": Initial karma cap.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.TheMark">
            <summary>"the_mark": Start with mark of communication.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.TheGlow">
            <summary>"the_glow": Start glowing.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.StartRoom">
            <summary>"start_room": Initial room, plus backups from highest to lowest priority.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.GuideOverseer">
            <summary>"guide_overseer": Player guide overseer color index.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.HasDreams">
            <summary>"has_dreams": Whether or not to track dream state.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.UseDefaultDreams">
            <summary>"use_default_dreams": Whether or not to show Survivor's dreams.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.CycleLengthMin">
            <summary>"cycle_length_min": Minimum cycle length in minutes.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.CycleLengthMax">
            <summary>"cycle_length_max": Maximum cycle length in minutes.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.PermaUnlockGates">
            <summary>"perma_unlock_gates": Permanently unlock gates once used.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.FoodMin">
            <summary>"food_min": Food needed to sleep.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.FoodMax">
            <summary>"food_max": Maximum food stored during a cycle.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.SelectMenuScene">
            <summary>"select_menu_scene": The scene for this slugcat on the select menu.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.SelectMenuSceneAscended">
            <summary>"select_menu_scene_ascended": The scene for this slugcat on the select menu when ascended.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.SleepScene">
            <summary>"sleep_scene": The scene for this slugcat when hibernating.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.StarveScene">
            <summary>"starve_scene": The scene for this slugcat when losing from starvation.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.DeathScene">
            <summary>"death_scene": The scene for this slugcat when losing from a non-starvation death.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.IntroScene">
            <summary>"intro_scene": Add an intro scene in the style of Survivor or Gourmand.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.OutroScene">
            <summary>"outro_scene": Add an outro scene for a slugbase slugcat. </summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.WorldState">
            <summary>"world_state": The timeline to use for creature spawns and room connections.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.StoryRegions">
            <summary>"story_regions": The new or removed story regions from an inherited world_state.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.TimelineBefore">
            <summary>"timeline_before": The next timeline after this character's.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.TimelineAfter">
            <summary>"timeline_after": The previous timeline before this character's.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.TitleCard">
            <summary>"title_card": Add a intro titlecard to be randomly selected. Must be 1366x768 pixels big to display correctly.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.ExpeditionEnabled">
            <summary>"expedition_enabled": Enable Expedition mode for this character.</summary>
        </member>
        <member name="F:SlugBase.Features.GameFeatures.ExpeditionDescription">
            <summary>"expedition_description": Character description in Expedition mode.</summary>
        </member>
        <member name="T:SlugBase.Features.FeatureTypes">
            <summary>
            Helper methods to construct <see cref="T:SlugBase.Features.Feature`1"/>s with simple parsing rules.
            </summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerInt(System.String)">
            <summary>Create a player feature that takes one integer.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerLong(System.String)">
            <summary>Create a player feature that takes one integer.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerDouble(System.String)">
            <summary>Create a player feature that takes one number.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerFloat(System.String)">
            <summary>Create a player feature that takes one number.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerString(System.String)">
            <summary>Create a player feature that takes one string.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerSlugcatName(System.String)">
            <summary>Create a player feature that takes a slugcat name.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerInts(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of integers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerLongs(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of integers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerDoubles(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of numbers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerFloats(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of numbers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerStrings(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of strings.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerSlugcatNames(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of slugcat names.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerColor(System.String)">
            <summary>Create a player feature that takes a color.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerCustomColor(System.String)">
            <summary>Create a player feature that takes a palette-modified color.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerBool(System.String)">
            <summary>Create a player feature that takes one boolean.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerEnum``1(System.String)">
            <summary>Create a player feature that takes one enum value.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerExtEnum``1(System.String)">
            <summary>Create a player feature that takes one enum value.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.PlayerExtEnums``1(System.String,System.Int32,System.Int32)">
            <summary>Create a player feature that takes an array of enum values.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameInt(System.String)">
            <summary>Create a game feature that takes one integer.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameLong(System.String)">
            <summary>Create a game feature that takes one integer.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameDouble(System.String)">
            <summary>Create a game feature that takes one number.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameFloat(System.String)">
            <summary>Create a game feature that takes one number.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameString(System.String)">
            <summary>Create a game feature that takes one string.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameSlugcatName(System.String)">
            <summary>Create a game feature that takes a slugcat name.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameInts(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of integers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameLongs(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of integers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameDoubles(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of numbers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameFloats(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of numbers.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameStrings(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of strings.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameSlugcatNames(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of slugcat names.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameColor(System.String)">
            <summary>Create a game feature that takes a color.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameCustomColor(System.String)">
            <summary>Create a game feature that takes a palette-modified color.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameBool(System.String)">
            <summary>Create a game feature that takes one boolean.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameEnum``1(System.String)">
            <summary>Create a game feature that takes one enum value.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameExtEnum``1(System.String)">
            <summary>Create a game feature that takes one enum value.</summary>
        </member>
        <member name="M:SlugBase.Features.FeatureTypes.GameExtEnums``1(System.String,System.Int32,System.Int32)">
            <summary>Create a game feature that takes an array of enum values.</summary>
        </member>
        <member name="P:SlugBase.Features.WorldHooks.Separator">
            <summary>Path.DirectorySeparatorChar but shorter, because it takes too much space</summary>
        </member>
        <member name="T:SlugBase.JsonAny">
            <summary>
            A JSON object, list, number, or string.
            </summary>
        </member>
        <member name="M:SlugBase.JsonAny.AsObject">
            <summary>Cast to <see cref="T:SlugBase.JsonObject"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a JSON object.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsList">
            <summary>Cast to <see cref="T:SlugBase.JsonList"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a JSON list.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsDouble">
            <summary>Cast to <see cref="T:System.Double"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsLong">
            <summary>Cast to <see cref="T:System.Int64"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsString">
            <summary>Cast to <see cref="T:System.String"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a string.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsInt">
            <summary>Cast to <see cref="T:System.Int32"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsFloat">
            <summary>Cast to <see cref="T:System.Single"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsBool">
            <summary>Cast to <see cref="T:System.Boolean"/>.</summary>
            <exception cref="T:SlugBase.JsonException">This isn't a boolean.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.TryObject">
            <summary>Try casting to <see cref="T:SlugBase.JsonObject"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryList">
            <summary>Try casting to <see cref="T:SlugBase.JsonList"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryLong">
            <summary>Try casting to <see cref="T:System.Int64"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryInt">
            <summary>Try casting to <see cref="T:System.Int32"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryDouble">
            <summary>Try casting to <see cref="T:System.Double"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryFloat">
            <summary>Try casting to <see cref="T:System.Single"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryString">
            <summary>Try casting to <see cref="T:System.String"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.TryBool">
            <summary>Try casting to <see cref="T:System.Boolean"/>, returning <c>null</c> on failure.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.IsNull">
            <summary>Test if this value is <c>null</c>.</summary>
        </member>
        <member name="M:SlugBase.JsonAny.AsObject(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:SlugBase.JsonObject"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a JSON object.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsList(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:SlugBase.JsonList"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a JSON list.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsDouble(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:System.Double"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsFloat(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:System.Single"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsLong(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:System.Int64"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsInt(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:System.Int32"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a number.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsString(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:System.String"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a string.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.AsBool(SlugBase.JsonAny)">
            <summary>Cast to <see cref="T:System.Boolean"/>.</summary>
            <exception cref="T:SlugBase.JsonException"><paramref name="json"/> isn't a boolean.</exception>
        </member>
        <member name="M:SlugBase.JsonAny.Parse(System.String)">
            <summary>
            Parse JSON text as a <see cref="T:SlugBase.JsonAny"/>.
            </summary>
            <param name="data">The JSON text.</param>
            <returns>A <see cref="T:SlugBase.JsonAny"/> representing the root element.</returns>
            <exception cref="T:SlugBase.JsonParseException">The root element could not be parsed.</exception>
        </member>
        <member name="P:SlugBase.JsonAny.Type">
            <summary>
            The type of this element.
            </summary>
        </member>
        <member name="T:SlugBase.JsonAny.Element">
            <summary>
            Represents the type of a JSON element.
            </summary>
        </member>
        <member name="F:SlugBase.JsonAny.Element.Object">
            <summary>
            Values associated with string keys.
            </summary>
        </member>
        <member name="F:SlugBase.JsonAny.Element.List">
            <summary>
            Values associated with integer keys.
            </summary>
        </member>
        <member name="F:SlugBase.JsonAny.Element.Number">
            <summary>
            A number.
            </summary>
        </member>
        <member name="F:SlugBase.JsonAny.Element.String">
            <summary>
            A string.
            </summary>
        </member>
        <member name="F:SlugBase.JsonAny.Element.Bool">
            <summary>
            A boolean.
            </summary>
        </member>
        <member name="F:SlugBase.JsonAny.Element.Null">
            <summary>
            A <c>null</c> value.
            </summary>
        </member>
        <member name="T:SlugBase.JsonObject">
            <summary>
            A JSON object.
            </summary>
        </member>
        <member name="M:SlugBase.JsonObject.Get(System.String)">
            <summary>
            Get an element from this object.
            </summary>
            <param name="key">The JSON property to search for.</param>
            <returns>The found value.</returns>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.TryGet(System.String)">
            <summary>
            Get an element from this object.
            </summary>
            <param name="key">The JSON property to search for.</param>
            <returns>The found value, or <c>null</c> if the property doesn't exist.</returns>
        </member>
        <member name="M:SlugBase.JsonObject.GetObject(System.String)">
            <summary>Get a <see cref="T:SlugBase.JsonObject"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetList(System.String)">
            <summary>Get a <see cref="T:SlugBase.JsonList"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetDouble(System.String)">
            <summary>Get a <see cref="T:System.Double"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetFloat(System.String)">
            <summary>Get a <see cref="T:System.Single"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetLong(System.String)">
            <summary>Get a <see cref="T:System.Int64"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetInt(System.String)">
            <summary>Get an <see cref="T:System.Int32"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetString(System.String)">
            <summary>Get a <see cref="T:System.String"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.GetBool(System.String)">
            <summary>Get a <see cref="T:System.Boolean"/> from this object.</summary>
            <param name="key">The JSON property to search for.</param>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist or was the wrong type.</exception>
        </member>
        <member name="P:SlugBase.JsonObject.Item(System.String)">
            <summary>
            Get an element from this object.
            </summary>
            <param name="key">The JSON property to search for.</param>
            <returns>The found value.</returns>
            <exception cref="T:SlugBase.JsonException">The specified property doesn't exist.</exception>
        </member>
        <member name="M:SlugBase.JsonObject.op_Implicit(SlugBase.JsonObject)~SlugBase.JsonAny">
            <summary>
            Get <paramref name="obj"/> as a <see cref="T:SlugBase.JsonAny"/>.
            </summary>
        </member>
        <member name="M:SlugBase.JsonObject.GetEnumerator">
            <summary>
            Get an enumerator for all properties and values of this object.
            </summary>
        </member>
        <member name="T:SlugBase.JsonList">
            <summary>
            A JSON list.
            </summary>
        </member>
        <member name="M:SlugBase.JsonList.Get(System.Int32)">
            <summary>
            Get an element from this list.
            </summary>
            <param name="key">The index.</param>
            <returns>The found value.</returns>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range.</exception>
        </member>
        <member name="M:SlugBase.JsonList.TryGet(System.Int32)">
            <summary>
            Get an element from this list.
            </summary>
            <param name="key">The index.</param>
            <returns>The found value, or <c>null</c> if <paramref name="key"/> is out of range.</returns>
        </member>
        <member name="M:SlugBase.JsonList.GetObject(System.Int32)">
            <summary>Get a <see cref="T:SlugBase.JsonObject"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetList(System.Int32)">
            <summary>Get a <see cref="T:SlugBase.JsonList"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetDouble(System.Int32)">
            <summary>Get a <see cref="T:System.Double"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetFloat(System.Int32)">
            <summary>Get a <see cref="T:System.Single"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetLong(System.Int32)">
            <summary>Get a <see cref="T:System.Int64"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetInt(System.Int32)">
            <summary>Get an <see cref="T:System.Int32"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetString(System.Int32)">
            <summary>Get a <see cref="T:System.String"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="M:SlugBase.JsonList.GetBool(System.Int32)">
            <summary>Get a <see cref="T:System.Boolean"/> from this list.</summary>
            <param name="key">The index.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range or is not the right type.</exception>
        </member>
        <member name="P:SlugBase.JsonList.Count">
            <summary>
            Get the number of elements in this list.
            </summary>
        </member>
        <member name="P:SlugBase.JsonList.Item(System.Int32)">
            <summary>
            Get an element from this list.
            </summary>
            <param name="key">The index.</param>
            <returns>The found value.</returns>
            <exception cref="T:SlugBase.JsonException"><paramref name="key"/> is out of range.</exception>
        </member>
        <member name="M:SlugBase.JsonList.op_Implicit(SlugBase.JsonList)~SlugBase.JsonAny">
            <summary>
            Get <paramref name="list"/> as a <see cref="T:SlugBase.JsonAny"/>.
            </summary>
        </member>
        <member name="M:SlugBase.JsonList.GetEnumerator">
            <summary>
            Gets an enumerator for all elements of this list.
            </summary>
        </member>
        <member name="T:SlugBase.JsonException">
            <summary>
            Represents errors that occur when accessing JSON data.
            </summary>
        </member>
        <member name="P:SlugBase.JsonException.JsonPath">
            <summary>
            The path to the element that failed to parse, starting from the root object.
            </summary>
        </member>
        <member name="M:SlugBase.JsonException.#ctor(SlugBase.JsonAny)">
            <summary>Initializes a new instance of the <see cref="T:SlugBase.JsonException"/> class with a path to the invalid element.</summary>
        </member>
        <member name="M:SlugBase.JsonException.#ctor(System.String,SlugBase.JsonAny)">
            <summary>Initializes a new instance of the <see cref="T:SlugBase.JsonException"/> class with a specified error message
            and a path to the invalid element.</summary>
        </member>
        <member name="M:SlugBase.JsonException.#ctor(System.String,System.Exception,SlugBase.JsonAny)">
            <summary>Initializes a new instance of the <see cref="T:SlugBase.JsonException"/> class with a specified error message,
            a reference to the inner exception that is the cause of this exception, and a path to the invalid element.</summary>
        </member>
        <member name="T:SlugBase.JsonConverter">
            <summary>
            Converts to and from JSON values.
            </summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>Create a read-only <see cref="T:SlugBase.JsonObject"/> from a copy of <paramref name="obj"/>.</summary>
            <remarks>If <c>null</c> values are valid, use <see cref="M:SlugBase.JsonConverter.ToJsonAny(System.Object)"/> instead.</remarks>
            <exception cref="T:System.ArgumentException">An object in <paramref name="obj"/> could not be converted to JSON.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="obj"/> was <c>null</c>.</exception>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Collections.Generic.List{System.Object})">
            <summary>Create a read-only <see cref="T:SlugBase.JsonList"/> from a copy of <paramref name="list"/>.</summary>
            <remarks>If <c>null</c> values are valid, use <see cref="M:SlugBase.JsonConverter.ToJsonAny(System.Object)"/> instead.</remarks>
            <exception cref="T:System.ArgumentException">An object in <paramref name="list"/> could not be converted to JSON.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="list"/> was <c>null</c>.</exception>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.String)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.String"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Boolean)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.Boolean"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Double)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.Double"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Single)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.Single"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Int32)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.Int32"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.UInt32)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.UInt32"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Int16)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.Int16"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.UInt16)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.UInt16"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.Int64)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.Int64"/>. <paramref name="value"/> is converted to a <see cref="T:System.Double"/>, so very large numbers may lose precision.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJson(System.UInt64)">
            <summary>Create a <see cref="T:SlugBase.JsonAny"/> from a single <see cref="T:System.UInt64"/>. <paramref name="value"/> is converted to a <see cref="T:System.Double"/>, so very large numbers may lose precision.</summary>
        </member>
        <member name="M:SlugBase.JsonConverter.ToJsonAny(System.Object)">
            <summary>Create a read-only <see cref="T:SlugBase.JsonAny"/> from a copy of <paramref name="value"/>.</summary>
            <exception cref="T:System.ArgumentException">An object in the list or dictionary could not be converted to JSON.</exception>
        </member>
        <member name="M:SlugBase.JsonConverter.ToDictionary(SlugBase.JsonObject)">
            <summary>
            Create a mutable copy of a <see cref="T:SlugBase.JsonObject"/>.
            </summary>
            <param name="value">The <see cref="T:SlugBase.JsonObject"/> to copy.</param>
            <returns>A copy of <paramref name="value"/> as a <see cref="T:System.Collections.Generic.Dictionary`2"/>.</returns>
        </member>
        <member name="M:SlugBase.JsonConverter.ToList(SlugBase.JsonList)">
            <summary>
            Create a mutable copy of a <see cref="T:SlugBase.JsonList"/>.
            </summary>
            <param name="value">The <see cref="T:SlugBase.JsonList"/> to copy.</param>
            <returns>A copy of <paramref name="value"/> as a <see cref="T:System.Collections.Generic.List`1"/>.</returns>
        </member>
        <member name="M:SlugBase.JsonConverter.ToObject(SlugBase.JsonAny)">
            <summary>
            Create a mutable copy of a <see cref="T:SlugBase.JsonAny"/>.
            </summary>
            <param name="value">The <see cref="T:SlugBase.JsonAny"/> to copy.</param>
            <returns>A copy of <paramref name="value"/> as a <see cref="T:System.Collections.Generic.List`1"/>, <see cref="T:System.Collections.Generic.Dictionary`2"/>, <see cref="T:System.String"/>, <see cref="T:System.Boolean"/>, <see cref="T:System.Double"/>, or <c>null</c>.</returns>
        </member>
        <member name="T:SlugBase.JsonParseException">
            <summary>
            Represents errors that occur when parsing JSON data.
            </summary>
        </member>
        <member name="P:SlugBase.JsonParseException.CharIndex">
            <summary>
            The offset in the input string that the error occurred at.
            </summary>
        </member>
        <member name="P:SlugBase.JsonParseException.Line">
            <summary>
            The line in the input string that the error occurred at.
            </summary>
        </member>
        <member name="T:SlugBase.JsonRegistry`2">
            <summary>
            Represents a collection of values with unique IDs that can be loaded from JSON.
            </summary>
            <typeparam name="TKey">The type of the <see cref="T:ExtEnum`1"/> keys.</typeparam>
            <typeparam name="TValue">The type of the values.</typeparam>
        </member>
        <member name="E:SlugBase.JsonRegistry`2.EntryReloaded">
            <summary>
            Occurs when a loaded JSON file is modified while <see cref="P:SlugBase.JsonRegistry`2.WatchForChanges"/> is set.
            </summary>
        </member>
        <member name="E:SlugBase.JsonRegistry`2.LoadFailed">
            <summary>
            Occurs when a JSON file fails to load or reload.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.WatchForChanges">
            <summary>
            Whether this registry should track files to reload.
            Call <see cref="M:SlugBase.JsonRegistry`2.ReloadChangedFiles"/> to apply changes.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.Keys">
            <summary>
            A collection of all keys used by this registry.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.Values">
            <summary>
            A collection of all values registered.
            </summary>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.#ctor(System.Func{`0,SlugBase.JsonObject,`1})">
            <summary>
            Create a new registry.
            </summary>
            <param name="fromJson">The factory that creates values from JSON.</param>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.ScanDirectory(System.String)">
            <summary>
            Register all JSON files in a directory using <see cref="M:SlugBase.JsonRegistry`2.AddFromFile(System.String)"/>.
            </summary>
            <param name="directory">The directory to search.</param>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.AddFromFile(System.String)">
            <summary>
            Parse a file as JSON and link it to a new <see cref="T:ExtEnum`1"/> ID.
            </summary>
            <param name="path">The file path to the json.</param>
            <returns>The registered key and value.</returns>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.TryAddFromFile(System.String)">
            <summary>
            Parse a file as JSON and link it to a new <see cref="T:ExtEnum`1"/> ID.
            Load errors can be monitored with <see cref="E:SlugBase.JsonRegistry`2.LoadFailed"/>.
            </summary>
            <param name="path">The file path to the json.</param>
            <returns>The registered key and value, or <c>null</c> if loading failed.</returns>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.Add(SlugBase.JsonObject)">
            <summary>
            Load a <typeparamref name="TValue"/> from JSON and link it to a new <see cref="T:ExtEnum`1"/> ID.
            </summary>
            <param name="json">The json data for the new object.</param>
            <returns>The registered key and value.</returns>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.Remove(`0)">
            <summary>
            Unregister a value.
            </summary>
            <param name="id"></param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.TryGet(`0,`1@)">
            <summary>
            Get a registered value by key.
            </summary>
            <param name="id">The unique ID of the value.</param>
            <param name="value">The registered value with the unique ID.</param>
            <returns><c>true</c> if the value was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.GetOrDefault(`0)">
            <summary>
            Get a registered value by key.
            </summary>
            <param name="id">The unique ID of the value.</param>
            <returns>The registered value with the unique ID.</returns>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.TryGetPath(`0,System.String@)">
            <summary>
            Gets the path to a registered value by key.
            </summary>
            <param name="id">The unique ID of the value.</param>
            <param name="path">The path to the value's JSON file.</param>
            <returns><c>true</c> if the value was found and has a path, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.JsonRegistry`2.ReloadChangedFiles">
            <summary>
            Reload all JSON files that have been modified.
            </summary>
        </member>
        <member name="T:SlugBase.JsonRegistry`2.ReloadedEventArgs">
            <summary>
            Provides data for the <see cref="E:SlugBase.JsonRegistry`2.EntryReloaded"/> event.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.ReloadedEventArgs.Key">
            <summary>
            The unique key of the reloaded entry.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.ReloadedEventArgs.Value">
            <summary>
            The reloaded entry.
            </summary>
        </member>
        <member name="T:SlugBase.JsonRegistry`2.LoadErrorEventArgs">
            <summary>
            Provides data for the <see cref="E:SlugBase.JsonRegistry`2.LoadFailed"/> event.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.LoadErrorEventArgs.Exception">
            <summary>
            The exception that caused this event, or <c>null</c> if it was not from an exception.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.LoadErrorEventArgs.ErrorMessage">
            <summary>
            An error message that may be shown to the user.
            </summary>
        </member>
        <member name="P:SlugBase.JsonRegistry`2.LoadErrorEventArgs.Path">
            <summary>
            The path to the file that errored, or <c>null</c> if it was not loaded from a file.
            </summary>
        </member>
        <member name="T:SlugBase.JsonUtils">
            <summary>
            Converts <see cref="T:SlugBase.JsonAny"/> to other types.
            </summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToLong(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.Int64"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToInt(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.Int32"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToDouble(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.Double"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToFloat(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.Single"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToString(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.String"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToBool(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.Boolean"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToSlugcatName(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:SlugcatStats.Name"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToColor(SlugBase.JsonAny)">
            <summary>
            Convert to <see cref="T:UnityEngine.Color"/>.
            </summary>
            <remarks>
            This may be a hex string or equivalent integer; list of components; or object with "r", "g", "b", and possibly "a" properties.
            </remarks>
        </member>
        <member name="M:SlugBase.JsonUtils.ToPlayerColor(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:SlugBase.DataTypes.PlayerColor"/> via <see cref="M:SlugBase.DataTypes.PlayerColor.#ctor(SlugBase.JsonAny)"/>.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToLongs(SlugBase.JsonAny)">
            <summary>Convert list to <see cref="T:System.Int64"/>[].</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToInts(SlugBase.JsonAny)">
            <summary>Convert list to <see cref="T:System.Int32"/>[].</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToDoubles(SlugBase.JsonAny)">
            <summary>Convert list to <see cref="T:System.Double"/>[].</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToFloats(SlugBase.JsonAny)">
            <summary>Convert list to <see cref="T:System.Single"/>[].</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToStrings(SlugBase.JsonAny)">
            <summary>Convert list to <see cref="T:System.String"/>[].</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToSlugcatNames(SlugBase.JsonAny)">
            <summary>Convert list to <see cref="T:SlugcatStats.Name"/>[].</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToEnum``1(SlugBase.JsonAny)">
            <summary>Convert to <see cref="T:System.Enum"/> value.</summary>
        </member>
        <member name="M:SlugBase.JsonUtils.ToExtEnum``1(SlugBase.JsonAny)">
            <summary>
            Convert to <see cref="T:ExtEnum`1"/> value.
            </summary>
            <remarks>
            <see cref="P:ExtEnumBase.Index"/> will be -1 for values that could not be parsed.
            </remarks>
        </member>
        <member name="M:SlugBase.JsonUtils.ToExtEnums``1(SlugBase.JsonAny)">
            <summary>
            Convert list to <see cref="T:ExtEnum`1"/>[].
            </summary>
            <remarks>
            <see cref="P:ExtEnumBase.Index"/> will be -1 for values that could not be parsed.
            </remarks>
        </member>
        <member name="M:SlugBase.JsonUtils.ToVector2(SlugBase.JsonAny)">
            <summary>
            Convert to <see cref="T:UnityEngine.Vector2"/>
            </summary>
            <remarks>
            This may be a list of components or an object with "x" and "y" properties.
            </remarks>
        </member>
        <member name="T:SlugBase.SaveData.MinedSaveData">
            <summary>
            Holds all the slugcat associated data that is needed outside of loaded savestate
            </summary>
        </member>
        <member name="F:SlugBase.SaveData.MinedSaveData.Data">
            <summary>
            Registry of all additional data for each savegamedata
            </summary>
        </member>
        <member name="M:SlugBase.SaveData.MinedSaveData.#ctor(RainWorld,SlugcatStats.Name)">
            <summary>
            Mines select menu scene from saved data
            </summary>
            <param name="rainWorld"></param>
            <param name="slugcat"></param>
        </member>
        <member name="M:SlugBase.SaveData.MinedSaveData.Mine``1(System.String,System.String,System.String,``0)">
            <summary>
            Finds a base64 encoded json string inside saveline and tries to unpack it into an object
            </summary>
            <typeparam name="T">Type of object that is being looked for</typeparam>
            <param name="saveLine">Line to search object in</param>
            <param name="startMarker">Left delimiter of saved value</param>
            <param name="endMarker">Right delimiter of saved value</param>
            <param name="defaultValue">What to return if the process failed</param>
            <returns>instance of T saved inside string</returns>
        </member>
        <member name="T:SlugBase.SaveData.SaveDataExtension">
            <summary>
            Extensions to generate the <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> helper from the game's save data.
            </summary>
        </member>
        <member name="M:SlugBase.SaveData.SaveDataExtension.GetSlugBaseData(DeathPersistentSaveData)">
            <summary>
            Gets a <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> from the game's <see cref="T:DeathPersistentSaveData"/>.
            </summary>
            <param name="data">The <see cref="T:DeathPersistentSaveData"/> instance.</param>
            <returns>A <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> bound to the <see cref="T:DeathPersistentSaveData"/>.</returns>
        </member>
        <member name="M:SlugBase.SaveData.SaveDataExtension.GetSlugBaseData(MiscWorldSaveData)">
            <summary>
            Gets a <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> from the game's <see cref="T:MiscWorldSaveData"/>.
            </summary>
            <param name="data">The <see cref="T:MiscWorldSaveData"/> instance.</param>
            <returns>A <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> bound to the <see cref="T:MiscWorldSaveData"/> instance.</returns>
        </member>
        <member name="M:SlugBase.SaveData.SaveDataExtension.GetSlugBaseData(PlayerProgression.MiscProgressionData)">
            <summary>
            Gets a <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> from the game's <see cref="T:PlayerProgression.MiscProgressionData"/>.
            </summary>
            <param name="data">The <see cref="T:PlayerProgression.MiscProgressionData"/> instance.</param>
            <returns>A <see cref="T:SlugBase.SaveData.SlugBaseSaveData"/> bound to the <see cref="T:PlayerProgression.MiscProgressionData"/> instance.</returns>
        </member>
        <member name="T:SlugBase.SaveData.SlugBaseSaveData">
            <summary>
            A helper for interacting with the game's save data.
            </summary>
        </member>
        <member name="M:SlugBase.SaveData.SlugBaseSaveData.TryGet``1(System.String,``0@)">
            <summary>
            Gets a value from the save data.
            </summary>
            <param name="key">The key for retrieving the value.</param>
            <param name="value">The stored value.</param>
            <typeparam name="T">The value's type.</typeparam>
            <returns><c>true</c> if a stored value was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.SaveData.SlugBaseSaveData.Set``1(System.String,``0)">
            <summary>
            Sets or adds a value to the save data.
            </summary>
            <param name="key">The key for storing the value.</param>
            <param name="value">The value to be stored.</param>
            <typeparam name="T">The value's type.</typeparam>
        </member>
        <member name="M:SlugBase.SaveData.SlugBaseSaveData.Remove(System.String)">
            <summary>
            Removes a value from the save data.
            </summary>
            <param name="key">The key for removing the value.</param>
            <returns><see langword="true"/> if the key was found and removed, <see langword="false"/> otherwise.</returns>
        </member>
        <member name="T:SlugBase.SlugBaseCharacter">
            <summary>
            A character added by SlugBase.
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.Registry">
            <summary>
            Stores all registered <see cref="T:SlugBase.SlugBaseCharacter"/>s.
            </summary>
        </member>
        <member name="E:SlugBase.SlugBaseCharacter.Refreshed">
            <summary>
            Occurs when any <see cref="T:SlugBase.SlugBaseCharacter"/>'s JSON file is modified, after all features have been loaded.
            </summary>
            <remarks>
            This event is only raised when in-game.
            </remarks>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.TryGet(SlugcatStats.Name,SlugBase.SlugBaseCharacter@)">
            <summary>
            Gets a <see cref="T:SlugBase.SlugBaseCharacter"/> by <paramref name="name"/>.
            </summary>
            <param name="name">The <see cref="T:SlugcatStats.Name"/> to search for.</param>
            <param name="character">The <see cref="T:SlugBase.SlugBaseCharacter"/> with the given <paramref name="name"/>, or <c>null</c> if it was not found.</param>
            <returns><c>true</c> if the <see cref="T:SlugBase.SlugBaseCharacter"/> was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.Get(SlugcatStats.Name)">
            <summary>
            Gets a <see cref="T:SlugBase.SlugBaseCharacter"/> by <paramref name="name"/>.
            </summary>
            <param name="name">The <see cref="T:SlugcatStats.Name"/> to search for.</param>
            <returns>The <see cref="T:SlugBase.SlugBaseCharacter"/>, or <c>null</c> if it was not found.</returns>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.Create(System.String)">
            <summary>
            Creates a new, blank <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
            <remarks>
            Use <see cref="P:SlugBase.SlugBaseCharacter.DisplayName"/>, <see cref="P:SlugBase.SlugBaseCharacter.Description"/>, and <see cref="P:SlugBase.SlugBaseCharacter.Features"/> to customize this character.
            </remarks>
            <param name="id">The new character's unique ID.</param>
            <returns>A new <see cref="T:SlugBase.SlugBaseCharacter"/> with a default name, default description, and no features.</returns>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.Name">
            <summary>
            This character's unique name.
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.DisplayName">
            <summary>
            The displayed name of this character, such as "The Survivor", "The Monk", or "The Hunter".
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.Description">
            <summary>
            A description of this character that appears on the select menu.
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.Features">
            <summary>
            Settings, abilities, or other <see cref="T:SlugBase.Features.Feature"/>s of this character.
            </summary>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.UpdateLegacyTimeline">
            <summary>
            If necessary, registers or updates a character-specific timeline that uses the pre-Watcher world state system.
            </summary>
        </member>
        <member name="T:SlugBase.SlugBaseCharacter.FeatureList">
            <summary>
            Stores the <see cref="T:SlugBase.Features.Feature"/>s of a <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.FeatureList.TryGet``1(SlugBase.Features.Feature{``0},``0@)">
            <summary>
            Get the value of a <see cref="T:SlugBase.Features.Feature`1"/>.
            </summary>
            <typeparam name="T">The <see cref="T:SlugBase.Features.Feature`1"/>'s data type.</typeparam>
            <param name="feature">The feature to get data from.</param>
            <param name="value">The feature's data, or <typeparamref name="T"/>'s default value if it was not found.</param>
            <returns><c>true</c> if the feature was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.FeatureList.Set(SlugBase.Features.Feature,SlugBase.JsonAny)">
            <summary>
            Add a <see cref="T:SlugBase.Features.Feature"/> or replace an existing <see cref="T:SlugBase.Features.Feature"/>'s value.
            </summary>
            <remarks>
            Some features are only read occasionally, such as when starting a game or entering a room.
            Instead of modifying features during gameplay, consider defining a custom <see cref="T:SlugBase.Features.Feature`1"/> and corresponding <see cref="T:SlugBase.Features.Data`2"/>.
            </remarks>
            <param name="feature">The feature to add or replace.</param>
            <param name="value">The feature's new value.</param>
            <exception cref="T:SlugBase.JsonException"><paramref name="value"/> was not a valid value for <paramref name="feature"/>.</exception>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.FeatureList.Remove(SlugBase.Features.Feature)">
            <summary>
            Remove a <see cref="T:SlugBase.Features.Feature"/>.
            </summary>
            <remarks>
            Some features are only read occasionally, such as when starting a game or entering a room.
            Instead of modifying features during gameplay, consider defining a custom <see cref="T:SlugBase.Features.Feature`1"/> and corresponding <see cref="T:SlugBase.Features.Data`2"/>.
            </remarks>
            <param name="feature">The feature to remove.</param>
            <returns><c>true</c> if the feature was present, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.FeatureList.Contains(SlugBase.Features.Feature)">
            <summary>
            Check this list for a <see cref="T:SlugBase.Features.Feature"/>.
            </summary>
            <param name="feature">The <see cref="T:SlugBase.Features.Feature"/> to check for.</param>
            <returns><c>true</c> if <paramref name="feature"/> was found, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:SlugBase.SlugBaseCharacter.FeatureList.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through all features in this collection.
            </summary>
        </member>
        <member name="T:SlugBase.SlugBaseCharacter.RefreshEventArgs">
            <summary>
            Provides data for the <see cref="E:SlugBase.SlugBaseCharacter.Refreshed"/> event.
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.RefreshEventArgs.Game">
            <summary>
            The current <see cref="T:RainWorldGame"/>.
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.RefreshEventArgs.ID">
            <summary>
            The ID of the reloaded <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
        </member>
        <member name="P:SlugBase.SlugBaseCharacter.RefreshEventArgs.Character">
            <summary>
            The reloaded <see cref="T:SlugBase.SlugBaseCharacter"/>.
            </summary>
        </member>
        <member name="M:SlugBase.Utils.FirstValidEnum``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Gets the first non-null, registered enum in <paramref name="values"/>.
            </summary>
            <param name="values">The collection to search through.</param>
            <returns>A registered enum, or <c>null</c> if no enums in the list are registered.</returns>
        </member>
        <member name="M:SlugBase.Utils.AllValidEnums``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Gets all non-null, registered enums in <paramref name="values"/>.
            </summary>
            <param name="values">The collection to search through.</param>
        </member>
        <member name="M:SlugBase.Utils.GetName(System.String)">
            <summary>
            Gets the <see cref="T:SlugcatStats.Name"/> corresponding to <paramref name="text"/>, converting canon character names to code names.
            </summary>
            <param name="text">The name of the character to convert to an ID.</param>
        </member>
        <member name="M:SlugBase.Utils.MatchCaseInsensitiveEnum``1(System.String)">
            <summary>
            Changes the case of <paramref name="name"/> to match the corresponding enum in <typeparamref name="T"/>.
            </summary>
            <param name="name">The string to capitalize.</param>
            <returns>The value of an enum in <typeparamref name="T"/>, or <paramref name="name"/> if no matching entry exists.</returns>
        </member>
        <member name="M:SlugBase.Utils.StringsToColors(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Converts a list of hexadecimal strings to colors.
            </summary>
        </member>
        <member name="M:SlugBase.Utils.ColorsToStrings(System.Collections.Generic.IEnumerable{UnityEngine.Color})">
            <summary>
            Converts a list of colors to hexadecimal strings.
            </summary>
        </member>
    </members>
</doc>
