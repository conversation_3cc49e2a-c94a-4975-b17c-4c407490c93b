{"frames": {"HeadA0.png": {"frame": {"x": 2, "y": 2, "w": 96, "h": 120}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 120}, "sourceSize": {"w": 96, "h": 120}}, "HeadA1.png": {"frame": {"x": 100, "y": 2, "w": 96, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 102}, "sourceSize": {"w": 96, "h": 102}}, "HeadA2.png": {"frame": {"x": 198, "y": 2, "w": 96, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 102}, "sourceSize": {"w": 96, "h": 102}}, "HeadA3.png": {"frame": {"x": 296, "y": 2, "w": 96, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 102}, "sourceSize": {"w": 96, "h": 102}}, "HeadA4.png": {"frame": {"x": 394, "y": 2, "w": 90, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 90, "h": 102}, "sourceSize": {"w": 90, "h": 102}}, "HeadA5.png": {"frame": {"x": 486, "y": 2, "w": 96, "h": 114}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 114}, "sourceSize": {"w": 96, "h": 114}}, "HeadA6.png": {"frame": {"x": 584, "y": 2, "w": 96, "h": 120}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 120}, "sourceSize": {"w": 96, "h": 120}}, "HeadA7.png": {"frame": {"x": 682, "y": 2, "w": 102, "h": 108}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 102, "h": 108}, "sourceSize": {"w": 102, "h": 108}}, "HeadA8.png": {"frame": {"x": 786, "y": 2, "w": 102, "h": 90}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 102, "h": 90}, "sourceSize": {"w": 102, "h": 90}}, "HeadA9.png": {"frame": {"x": 890, "y": 2, "w": 108, "h": 96}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 96}, "sourceSize": {"w": 108, "h": 96}}, "HeadA10.png": {"frame": {"x": 2, "y": 124, "w": 108, "h": 96}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 96}, "sourceSize": {"w": 108, "h": 96}}, "HeadA11.png": {"frame": {"x": 112, "y": 124, "w": 108, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 102}, "sourceSize": {"w": 108, "h": 102}}, "HeadA12.png": {"frame": {"x": 222, "y": 124, "w": 108, "h": 108}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 108}, "sourceSize": {"w": 108, "h": 108}}, "HeadA13.png": {"frame": {"x": 332, "y": 124, "w": 114, "h": 114}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 114, "h": 114}, "sourceSize": {"w": 114, "h": 114}}, "HeadA14.png": {"frame": {"x": 448, "y": 124, "w": 120, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 120, "h": 102}, "sourceSize": {"w": 120, "h": 102}}, "HeadA15.png": {"frame": {"x": 570, "y": 124, "w": 144, "h": 90}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 144, "h": 90}, "sourceSize": {"w": 144, "h": 90}}, "HeadA16.png": {"frame": {"x": 716, "y": 124, "w": 144, "h": 90}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 144, "h": 90}, "sourceSize": {"w": 144, "h": 90}}, "HeadA17.png": {"frame": {"x": 862, "y": 124, "w": 144, "h": 96}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 144, "h": 96}, "sourceSize": {"w": 144, "h": 96}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "EXtemplateHead.png", "format": "RGBA8888", "size": {"w": 1008, "h": 240}, "scale": "1", "smartupdate": "$TexturePacker:SmartUpdate:67c8482ad0c0e2baae58505e923e64b2:74ce02e1680b2a11344510742bf0321d:9879b0c07ec6050778bc3fbde2f55dd1$"}}