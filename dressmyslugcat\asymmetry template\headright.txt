{"frames": {"RightHeadA0.png": {"frame": {"x": 2, "y": 2, "w": 96, "h": 120}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 120}, "sourceSize": {"w": 96, "h": 120}}, "RightHeadA1.png": {"frame": {"x": 100, "y": 2, "w": 96, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 102}, "sourceSize": {"w": 96, "h": 102}}, "RightHeadA2.png": {"frame": {"x": 198, "y": 2, "w": 96, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 102}, "sourceSize": {"w": 96, "h": 102}}, "RightHeadA3.png": {"frame": {"x": 296, "y": 2, "w": 96, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 102}, "sourceSize": {"w": 96, "h": 102}}, "RightHeadA4.png": {"frame": {"x": 394, "y": 2, "w": 90, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 90, "h": 102}, "sourceSize": {"w": 90, "h": 102}}, "RightHeadA5.png": {"frame": {"x": 486, "y": 2, "w": 96, "h": 114}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 114}, "sourceSize": {"w": 96, "h": 114}}, "RightHeadA6.png": {"frame": {"x": 584, "y": 2, "w": 96, "h": 120}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 96, "h": 120}, "sourceSize": {"w": 96, "h": 120}}, "RightHeadA7.png": {"frame": {"x": 682, "y": 2, "w": 102, "h": 108}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 102, "h": 108}, "sourceSize": {"w": 102, "h": 108}}, "RightHeadA8.png": {"frame": {"x": 786, "y": 2, "w": 102, "h": 90}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 102, "h": 90}, "sourceSize": {"w": 102, "h": 90}}, "RightHeadA9.png": {"frame": {"x": 890, "y": 2, "w": 108, "h": 96}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 96}, "sourceSize": {"w": 108, "h": 96}}, "RightHeadA10.png": {"frame": {"x": 2, "y": 124, "w": 108, "h": 96}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 96}, "sourceSize": {"w": 108, "h": 96}}, "RightHeadA11.png": {"frame": {"x": 112, "y": 124, "w": 108, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 102}, "sourceSize": {"w": 108, "h": 102}}, "RightHeadA12.png": {"frame": {"x": 222, "y": 124, "w": 108, "h": 108}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 108, "h": 108}, "sourceSize": {"w": 108, "h": 108}}, "RightHeadA13.png": {"frame": {"x": 332, "y": 124, "w": 114, "h": 114}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 114, "h": 114}, "sourceSize": {"w": 114, "h": 114}}, "RightHeadA14.png": {"frame": {"x": 448, "y": 124, "w": 120, "h": 102}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 120, "h": 102}, "sourceSize": {"w": 120, "h": 102}}, "RightHeadA15.png": {"frame": {"x": 570, "y": 124, "w": 144, "h": 90}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 144, "h": 90}, "sourceSize": {"w": 144, "h": 90}}, "RightHeadA16.png": {"frame": {"x": 716, "y": 124, "w": 144, "h": 90}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 144, "h": 90}, "sourceSize": {"w": 144, "h": 90}}, "RightHeadA17.png": {"frame": {"x": 862, "y": 124, "w": 144, "h": 96}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 144, "h": 96}, "sourceSize": {"w": 144, "h": 96}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "RightEXtemplateHead.png", "format": "RGBA8888", "size": {"w": 1008, "h": 240}, "scale": "1", "smartupdate": "$TexturePacker:SmartUpdate:67c8482ad0c0e2baae58505e923e64b2:74ce02e1680b2a11344510742bf0321d:9879b0c07ec6050778bc3fbde2f55dd1$"}}