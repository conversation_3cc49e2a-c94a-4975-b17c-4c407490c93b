[Message:   BepInEx] BepInEx 5.4.17.0 - RainWorld (5/4/2025 5:09:38 PM)
[Info   :   BepInEx] Running under Unity v2020.3.45.6687953
[Info   :   BepInEx] CLR runtime version: 4.0.30319.42000
[Info   :   BepInEx] Supports SRE: True
[Info   :   BepInEx] System platform: Bits64, Windows
[Message:   BepInEx] Preloader started
[Info   :   BepInEx] Loaded 1 patcher method from [BepInEx.Preloader 5.4.17.0]
[Info   :   BepInEx] Loaded 1 patcher method from [BepInEx.MonoMod.Loader 1.0.0.0]
[Info   :   BepInEx] Loaded 1 patcher method from [BepInEx.MultiFolderLoader 1.3.1.0]
[Info   :   BepInEx] Loaded 1 patcher method from [Dragons.PublicDragon 1.0.0.0]
[Info   :   BepInEx] 4 patcher plugins loaded
[Info   :MultiFolderLoader] [multifolderloader].enabledModsListPath found in doorstop_config.ini, enabling enabled mods list
[Info   :MultiFolderLoader] [multifolderloader].gameVersionPath and [multifolderloader].enabledVersionPath found in doorstop_config.ini, verifying game version
[Info   :MultiFolderLoader] Current game version is: v1.10.3, old game version was: v1.10.3
[Info   :MultiFolderLoader] [multifolderloader].whiteListPath found in doorstop_config.ini, enabling whitelist for cleanup
[Warning:MultiFolderLoader] Skipping loading [devtools] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [expedition] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [jollycoop] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [moreslugcats] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [rwremix] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [versioning] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [watcher] because it's not enabled
[Info   :MultiFolderLoader] Loading preloader patchers from mod
[Info   :MultiFolderLoader] Loading 0 preloader patchers from mods...
[Info   :   BepInEx] Patching [UnityEngine.CoreModule] with [BepInEx.Chainloader]
[Info   :   MonoMod] Collecting target assemblies from mods
[Message:   BepInEx] Preloader finished
[Message:   BepInEx] Chainloader ready
[Message:   BepInEx] Chainloader started
[Info   :MultiFolderLoader] Finding plugins from mods...
[Info   :   BepInEx] 9 plugins to load
[Info   :   BepInEx] Loading [Custom Regions Support ********]
[Message:Custom Regions Support] Custom Regions Support (v********) initialized, applying hooks...
[Message:Custom Regions Support] Finished applying hooks!
[Info   :   BepInEx] Loading [Dress My Slugcat 2.1.3]
[Info   :Dress My Slugcat] Loading plugin DressMySlugcat
[Info   :Dress My Slugcat] Plugin DressMySlugcat is loaded!
[Info   :   BepInEx] Loading [No stuck pups 1.0.3]
[Info   :   BepInEx] Loading [Mouse Drag 1.0.3]
[Debug  :Mouse Drag] RoomUpdateIL success
[Debug  :Mouse Drag] AbstractRoomUpdateIL success
[Debug  :Mouse Drag] RainWorldGameGrafUpdateIL success
[Debug  :Mouse Drag] BodyChunkUpdateIL success
[Debug  :Mouse Drag] RainWorldGameRawUpdateIL success
[Debug  :Mouse Drag] RainWorldGameUpdateIL success
[Info   :Mouse Drag] OnEnable called
[Info   :   BepInEx] Loading [Rain World Achievements 1.0.0]
[Info   :   BepInEx] Loading [Pom 3.0]
[Debug  :       Pom] ILHook body start
[Debug  :       Pom] Found inj point, emitting
[Debug  :       Pom] emit complete
[Info   :   BepInEx] Loading [RegionKit 3.17.0]
[Info   :   BepInEx] Loading [Sticky HUD 1.1.0]
[Info   :   BepInEx] Loading [No Crash Penalty 1.0.0]
[Message:   BepInEx] Chainloader startup complete
[Debug  :Mouse Drag] RainWorldOnModsInitHook, first time initializing options interface and sprites
[Debug  :Mouse Drag] LoadSprites called
[Message: RegionKit] Registering module Assets
[Message: RegionKit] Registering module Common Hooks
[Message: RegionKit] Registering module CustomSSSong
[Message: RegionKit] Registering module RoomLoader
[Message: RegionKit] Registering module The Mast
[Message: RegionKit] Registering module Shelter Behaviors
[Message: RegionKit] Registering module Shader Tools
[Message: RegionKit] Registering module Room zones
[Message: RegionKit] Registering module Room Slideshow
[Message: RegionKit] Registering module Particles
[Message: RegionKit] Registering module MiscObjects
[Message: RegionKit] Registering module Multi-Color Snow
[Message: RegionKit] Registering module Miscellanceous
[Message: RegionKit] Registering module Machinery
[Message: RegionKit] Registering module Insects
[Message: RegionKit] Registering module IndividualPlacedObjectViewer
[Message: RegionKit] Registering module Iggy
[Message: RegionKit] Registering module GateCustomization
[Message: RegionKit] Registering module Effects
[Message: RegionKit] Registering module Echo Extender
[Message: RegionKit] Registering module DevUI
[Message: RegionKit] Registering module CustomProjections
[Message: RegionKit] Registering module Concealed Garden
[Message: RegionKit] Registering module Climbables
[Message: RegionKit] Registering module BackgroundBuilder
[Message: RegionKit] Registering module AridBarrens
[Message: RegionKit] Registering module Animated Decals
[Debug  : RegionKit] Scanned RegionKit, Version=********, Culture=neutral, PublicKeyToken=null for modules in 00:00:00.0178468
[Debug  : RegionKit] setup Assets
[Debug  : RegionKit] enable Assets
[Message: RegionKit] Assets module loading atlases from assetpath assets/regionkit
[Debug  : RegionKit] assets/regionkit/clippy loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/bigkarmashrine loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/deviggy loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/extendedgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/fan loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/gatecustomization loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/littleplanet loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/littleplanetring loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/shortcutcannon loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/spiketip loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/symbol_pearlchain loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/tatgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/themast loading as atlas
[Debug  : RegionKit] Resources loaded in 00:00:00.1558504
[Debug  : RegionKit] setup Common Hooks
[Debug  : RegionKit] enable Common Hooks
[Debug  : RegionKit] setup CustomSSSong
[Debug  : RegionKit] enable CustomSSSong
[Debug  : RegionKit] setup RoomLoader
[Debug  : RegionKit] enable RoomLoader
[Debug  : RegionKit] setup The Mast
[Debug  : RegionKit] enable The Mast
[Debug  : RegionKit] setup Shelter Behaviors
[Debug  : RegionKit] enable Shelter Behaviors
[Debug  : RegionKit] setup Shader Tools
[Debug  : RegionKit] enable Shader Tools
[Debug  : RegionKit] setup Room zones
[Debug  : RegionKit] enable Room zones
[Debug  : RegionKit] setup Room Slideshow
[Debug  : RegionKit] enable Room Slideshow
[Debug  : RegionKit] SetShader { InstantlyProgress = True, shader = Basic }, SetDelay { InstantlyProgress = True, newDelay = 40 }, SetInterpolation { InstantlyProgress = True, interpolator = RegionKit.Modules.RoomSlideShow.Interpolator, value = Linear, channels = RegionKit.Modules.RoomSlideShow.Channel[] }, SetInterpolation { InstantlyProgress = True, interpolator = RegionKit.Modules.RoomSlideShow.Interpolator, value = Quadratic, channels = RegionKit.Modules.RoomSlideShow.Channel[] }, SetContainer { InstantlyProgress = True, newContainer = Foreground }, Frame { InstantlyProgress = False, index = 5, elementName = LizardHead0.1, ticksDuration = 60, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 6, elementName = Circle20, ticksDuration = 40, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 7, elementName = LizardHead0.2, ticksDuration = 60, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 8, elementName = Circle20, ticksDuration = 40, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 9, elementName = LizardHead0.1, ticksDuration = 60, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }
[Debug  : RegionKit] setup Particles
[Debug  : RegionKit] enable Particles
[Debug  : RegionKit] setup MiscObjects
[Debug  : RegionKit] enable MiscObjects
[Debug  :Mouse Drag] RainWorldGameRawUpdateIL success
[Debug  :       Pom] ILHook body start
[Debug  :       Pom] Found inj point, emitting
[Debug  :       Pom] emit complete
[Debug  : RegionKit] setup Multi-Color Snow
[Debug  : RegionKit] enable Multi-Color Snow
[Debug  : RegionKit] setup Miscellanceous
[Debug  : RegionKit] enable Miscellanceous
[Debug  : RegionKit] setup Machinery
[Debug  : RegionKit] enable Machinery
[Debug  : RegionKit] setup Insects
[Debug  : RegionKit] enable Insects
[Debug  : RegionKit] setup IndividualPlacedObjectViewer
[Debug  : RegionKit] enable IndividualPlacedObjectViewer
[Debug  : RegionKit] setup Iggy
[Debug  : RegionKit] enable Iggy
[Debug  : RegionKit] setup GateCustomization
[Debug  : RegionKit] enable GateCustomization
[Debug  : RegionKit] setup Effects
[Message: RegionKit] entered loading / loading status: False
[Debug  : RegionKit] enable Effects
[Debug  : RegionKit] setup Echo Extender
[Debug  : RegionKit] enable Echo Extender
[Debug  : RegionKit] setup DevUI
[Debug  : RegionKit] enable DevUI
[Debug  : RegionKit] setup CustomProjections
[Debug  : RegionKit] enable CustomProjections
[Debug  : RegionKit] setup Concealed Garden
[Debug  : RegionKit] enable Concealed Garden
[Debug  :       Pom] ILHook body start
[Debug  :       Pom] Found inj point, emitting
[Debug  :       Pom] emit complete
[Debug  : RegionKit] setup Climbables
[Debug  : RegionKit] enable Climbables
[Debug  : RegionKit] setup BackgroundBuilder
[Debug  : RegionKit] enable BackgroundBuilder
[Debug  : RegionKit] setup AridBarrens
[Debug  : RegionKit] enable AridBarrens
[Debug  : RegionKit] setup Animated Decals
[Debug  : RegionKit] enable Animated Decals
[Message: RegionKit] Assets module loading atlases from assetpath assets/regionkit
[Debug  : RegionKit] assets/regionkit/clippy loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/bigkarmashrine loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/deviggy loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/extendedgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/fan loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/gatecustomization loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/littleplanet loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/littleplanetring loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/shortcutcannon loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/spiketip loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/symbol_pearlchain loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/tatgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/themast loading as atlas
[Debug  : RegionKit] Resources loaded in 00:00:00.0340089
[Debug  : RegionKit] Total load time 00:00:06.6437441
[Debug  : RegionKit] Load time for modules: 
[Debug  : RegionKit] 	Assets : 00:00:00.1570089
[Debug  : RegionKit] 	Common Hooks : 00:00:00.0259279
[Debug  : RegionKit] 	CustomSSSong : 00:00:00.0169459
[Debug  : RegionKit] 	RoomLoader : 00:00:00.0126420
[Debug  : RegionKit] 	The Mast : 00:00:00.2988749
[Debug  : RegionKit] 	Shelter Behaviors : 00:00:00.0126943
[Debug  : RegionKit] 	Shader Tools : 00:00:00.0124693
[Debug  : RegionKit] 	Room zones : 00:00:00.0005185
[Debug  : RegionKit] 	Room Slideshow : 00:00:00.0451643
[Debug  : RegionKit] 	Particles : 00:00:00.0033024
[Debug  : RegionKit] 	MiscObjects : 00:00:02.6574737
[Debug  : RegionKit] 	Multi-Color Snow : 00:00:00.1999281
[Debug  : RegionKit] 	Miscellanceous : 00:00:00.4736582
[Debug  : RegionKit] 	Machinery : 00:00:00.0319286
[Debug  : RegionKit] 	Insects : 00:00:00.1160626
[Debug  : RegionKit] 	IndividualPlacedObjectViewer : 00:00:00.0241133
[Debug  : RegionKit] 	Iggy : 00:00:00.0302967
[Debug  : RegionKit] 	GateCustomization : 00:00:00.1943884
[Debug  : RegionKit] 	Effects : 00:00:00.7275663
[Debug  : RegionKit] 	Echo Extender : 00:00:00.1089940
[Debug  : RegionKit] 	DevUI : 00:00:00.2547335
[Debug  : RegionKit] 	CustomProjections : 00:00:00.6879587
[Debug  : RegionKit] 	Concealed Garden : 00:00:00.0752666
[Debug  : RegionKit] 	Climbables : 00:00:00.0205109
[Debug  : RegionKit] 	BackgroundBuilder : 00:00:00.3462580
[Debug  : RegionKit] 	AridBarrens : 00:00:00.0003526
[Debug  : RegionKit] 	Animated Decals : 00:00:00.1080018
[Debug  :Mouse Drag] Integration.Hooks.Apply, finished applying enabled integration hook(s)
