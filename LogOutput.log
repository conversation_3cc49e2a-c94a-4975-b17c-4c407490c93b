[Message:   BepInEx] BepInEx 5.4.17.0 - RainWorld (7/20/2025 10:31:31 PM)
[Info   :   BepInEx] Running under Unity v2020.3.45.6687953
[Info   :   BepInEx] CLR runtime version: 4.0.30319.42000
[Info   :   BepInEx] Supports SRE: True
[Info   :   BepInEx] System platform: Bits64, Windows
[Message:   BepInEx] Preloader started
[Info   :   BepInEx] Loaded 1 patcher method from [BepInEx.Preloader 5.4.17.0]
[Info   :   BepInEx] Loaded 1 patcher method from [BepInEx.MonoMod.Loader 1.0.0.0]
[Info   :   BepInEx] Loaded 1 patcher method from [BepInEx.MultiFolderLoader 1.3.1.0]
[Info   :   BepInEx] Loaded 1 patcher method from [Dragons.PublicDragon 1.0.0.0]
[Info   :   BepInEx] 4 patcher plugins loaded
[Info   :MultiFolderLoader] [multifolderloader].enabledModsListPath found in doorstop_config.ini, enabling enabled mods list
[Info   :MultiFolderLoader] [multifolderloader].gameVersionPath and [multifolderloader].enabledVersionPath found in doorstop_config.ini, verifying game version
[Info   :MultiFolderLoader] Current game version is: v1.10.4, old game version was: v1.10.4
[Info   :MultiFolderLoader] [multifolderloader].whiteListPath found in doorstop_config.ini, enabling whitelist for cleanup
[Warning:MultiFolderLoader] Skipping loading [devtools] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [expedition] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [jollycoop] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [moreslugcats] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [RAGE'S SCUGS] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [rwremix] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [versioning] because it's not enabled
[Warning:MultiFolderLoader] Skipping loading [watcher] because it's not enabled
[Info   :MultiFolderLoader] Loading preloader patchers from mod
[Info   :MultiFolderLoader] Loading 0 preloader patchers from mods...
[Info   :   BepInEx] Patching [UnityEngine.CoreModule] with [BepInEx.Chainloader]
[Info   :   MonoMod] Collecting target assemblies from mods
[Message:   BepInEx] Preloader finished
[Message:   BepInEx] Chainloader ready
[Message:   BepInEx] Chainloader started
[Info   :MultiFolderLoader] Finding plugins from mods...
[Info   :   BepInEx] 16 plugins to load
[Info   :   BepInEx] Loading [Push to Meow 1.2.0]
[Info   :Push to Meow] initialised default sound IDs!
[Info   :Push to Meow] READY TO RRRRUMBL- i mean Meow Meow Meow Meow :3333
[Info   :   BepInEx] Loading [RainMeadow *******]
[Info   :RainMeadow] DeathContextualizer binded with ZapCoil
[Info   :RainMeadow] DeathContextualizer binded with SSOracleBehavior+ThrowOutBehavior
[Info   :RainMeadow] DeathContextualizer binded with DaddyCorruption+EatenCreature
[Info   :RainMeadow] DeathContextualizer binded with Player+Tongue
[Info   :RainMeadow] DeathContextualizer binded with LocustSystem+Swarm
[Info   :RainMeadow] DeathContextualizer binded with Player
[Info   :RainMeadow] DeathContextualizer binded with ElectricDeath
[Info   :RainMeadow] DeathContextualizer binded with Explosion
[Info   :RainMeadow] DeathContextualizer binded with KingTusks+Tusk
[Info   :RainMeadow] DeathContextualizer binded with Pomegranate
[Info   :RainMeadow] DeathContextualizer binded with UnderwaterShock
[Info   :RainMeadow] DeathContextualizer binded with Watcher.LightningMaker+StrikeAOE
[Info   :   BepInEx] Loading [Push to Meow - Rain Meadow Compatibility Add-on 1.1.1]
[Info   :Push to Meow - Rain Meadow Compatibility Add-on] Push to Meow comatability with Rain Meadow is probably enabled or something idk
[Info   :   BepInEx] Loading [Improved Input Config 2.0.4]
[Info   :   BepInEx] Loading [Revivify_Meadow 1.2.0]
[Info   :   BepInEx] Loading [Custom Slugcat Utils 1.0.0]
[Info   :   BepInEx] Loading [SlugBase 2.8.1]
[Info   :   BepInEx] Loading [Dress My Slugcat 2.1.5]
[Info   :Dress My Slugcat] Loading plugin DressMySlugcat
[Info   :Dress My Slugcat] Plugin DressMySlugcat is loaded!
[Info   :   BepInEx] Loading [fastergates 1.0.0]
[Info   :   BepInEx] Loading [InvAdd 0.1.0]
[Info   :   BepInEx] Loading [Music Volume Adjuster 1.0.5]
[Info   :   BepInEx] Loading [No Mod Update Confirm 1.0.0]
[Info   :   BepInEx] Loading [Extended Slugbase Features 1.1.0]
[Info   :   BepInEx] Loading [Pom 3.0]
[Debug  :       Pom] ILHook body start
[Debug  :       Pom] Found inj point, emitting
[Debug  :       Pom] emit complete
[Info   :   BepInEx] Loading [RegionKit 3.17.5]
[Info   :   BepInEx] Loading [Sticky HUD 1.1.0]
[Message:   BepInEx] Chainloader startup complete
[Info   :Improved Input Config] Adding new Input Actions
[Info   :Improved Input Config] New Input Actions 0 1 2 3 4 5 6 7 8 9 10 11 12 13 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50 51 52 53 54 55 56 57 58 59 60 61 62 63 64 
[Debug  :Improved Input Config] loading controller: iic|RewiredSaveData|playerName=Player0|dataType=ControllerMap|kv=2|controllerMapType=KeyboardMap|categoryId=0|layoutId=0|hardwareGuid=ae4830f9-63db-4d4c-90b3-1beb46ecaf49
data: 1.6|0|1|pushtomeow:meow|13|Button|Full
[Info   :RainMeadow] Check 1 path was: C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\scenes\main menu - downpour\main menu - downpour - flat.png file existed?: True
[Info   :RainMeadow] Check 2 path was: C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\mods\moreslugcats\illustrations\safari_ms.png file existed?: True
[Info   :RainMeadow] Check 3 path was: C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\mods\jollycoop\illustrations\jolly_title.png file existed?: True
[Info   :RainMeadow] Check 4 path was: C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\mods\expedition\illustrations\expeditiontitle.png file existed?: True
[Debug  :Improved Input Config] loading controller: iic|RewiredSaveData|playerName=Player0|dataType=ControllerMap|kv=2|controllerMapType=KeyboardMap|categoryId=0|layoutId=0|hardwareGuid=ae4830f9-63db-4d4c-90b3-1beb46ecaf49
data: 1.6|0|1|pushtomeow:meow|13|Button|Full
[Info   :RainMeadow] Options: ControlSetup from string KEYBOARD<ctrlA>0<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] Options: ControlSetup from string SPECIFIC_GAMEPAD<ctrlA>0<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] Options: ControlSetup from string SPECIFIC_GAMEPAD<ctrlA>1<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] Options: ControlSetup from string SPECIFIC_GAMEPAD<ctrlA>2<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Debug  :Improved Input Config] loading controller: iic|RewiredSaveData|playerName=Player0|dataType=ControllerMap|kv=2|controllerMapType=KeyboardMap|categoryId=0|layoutId=0|hardwareGuid=ae4830f9-63db-4d4c-90b3-1beb46ecaf49
data: 1.6|0|1|pushtomeow:meow|13|Button|Full
[Info   :RainMeadow] Options: ControlSetup from string KEYBOARD<ctrlA>0<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] Options: ControlSetup from string SPECIFIC_GAMEPAD<ctrlA>0<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] Options: ControlSetup from string SPECIFIC_GAMEPAD<ctrlA>1<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] Options: ControlSetup from string SPECIFIC_GAMEPAD<ctrlA>2<ctrlA><ctrlA>0<ctrlA>0<ctrlA>
[Info   :RainMeadow] world checksum: f28ce18e9cce039b751a489bc5bcc6a7
[Warning:RainMeadow] World Checksum INCORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] !! Synced load state
[Info   :RainMeadow] @M@ CORE FILE IS BEING CHANGED! :: NULL
[Info   :RainMeadow] @M@ STARTING MOUNT FOR CORE FILE
[Info   :Push to Meow] Registered OI
[Info   :Push to Meow] Loading custom slugcat meows:
[Info   :Push to Meow] Reading file c:\program files (x86)\steam\steamapps\workshop\content\312520\3257541402\pushtomeow\custom_meows.json
[Info   :Push to Meow] Loading meows from file c:\program files (x86)\steam\steamapps\workshop\content\312520\3257541402\pushtomeow\custom_meows.json (priority: -1)
[Info   :Push to Meow] Registered custom meow for slugcat ID "Gourmand":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowFatShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowFat
[Info   :Push to Meow]     short meow pup SoundID = (none)
[Info   :Push to Meow]      long meow pup SoundID = (none)
[Info   :Push to Meow]      volume mul: 1.15x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "Saint":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowSaintShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowSaint
[Info   :Push to Meow]     short meow pup SoundID = (none)
[Info   :Push to Meow]      long meow pup SoundID = (none)
[Info   :Push to Meow]      volume mul: 1x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "Inv":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowSofanthielShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowSofanthiel
[Info   :Push to Meow]     short meow pup SoundID = (none)
[Info   :Push to Meow]      long meow pup SoundID = (none)
[Info   :Push to Meow]      volume mul: 1x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "Artificer":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowCoarseShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowCoarse
[Info   :Push to Meow]     short meow pup SoundID = (none)
[Info   :Push to Meow]      long meow pup SoundID = (none)
[Info   :Push to Meow]      volume mul: 1.2x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "Spear":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowSpearShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowSpear
[Info   :Push to Meow]     short meow pup SoundID = (none)
[Info   :Push to Meow]      long meow pup SoundID = (none)
[Info   :Push to Meow]      volume mul: 0.55x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "White":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowNormalShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowNormal
[Info   :Push to Meow]     short meow pup SoundID = SlugcatMeowPupShort
[Info   :Push to Meow]      long meow pup SoundID = SlugcatMeowPup
[Info   :Push to Meow]      volume mul: 1x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "Yellow":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowNormalShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowNormal
[Info   :Push to Meow]     short meow pup SoundID = SlugcatMeowPupShort
[Info   :Push to Meow]      long meow pup SoundID = SlugcatMeowPup
[Info   :Push to Meow]      volume mul: 1x
[Info   :Push to Meow] 
[Info   :Push to Meow] Registered custom meow for slugcat ID "Red":
[Info   :Push to Meow]         short meow SoundID = SlugcatMeowNormalShort
[Info   :Push to Meow]          long meow SoundID = SlugcatMeowNormal
[Info   :Push to Meow]     short meow pup SoundID = SlugcatMeowPupShort
[Info   :Push to Meow]      long meow pup SoundID = SlugcatMeowPup
[Info   :Push to Meow]      volume mul: 1x
[Info   :Push to Meow] 
[Info   :Push to Meow] Successfully loaded 8 custom meow(s) :3
[Info   :Push to Meow] loaded custom meows :3
[Info   :Push to Meow] Adding Default Translations
[Info   :Push to Meow] Added LanguageID: "Spanish"
[Info   :Push to Meow] Added LanguageID: "French"
[Info   :Push to Meow] Added LanguageID: "Russian"
[Info   :Push to Meow] Added LanguageID: "Portuguese"
[Info   :Push to Meow] Added LanguageID: "German"
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.DeflateState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:No delta support for type: DeflateState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte[] bytes
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.AbstractCreatureState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:CreatureStateState creatureStateState
WorldCoordinate destination
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.CreatureStateStateFalseTrueFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.AbstractPhysicalObjectState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 8 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.ArtificialIntelligenceState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:DynamicOrderedStates`1 moduleStates
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AIModuleState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.FriendTrackerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:No delta support for type: FriendTrackerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean followClosestFriend
EntityId friend
Single desiredCloseness
Single tamingDifficlty
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntity+EntityIdTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.SocialMemoryState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:DynamicOrderedCustomSerializables`1 relationshipStates
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedCustomSerializables`1[[RainMeadow.SocialMemoryState+RelationshipState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.CreatureHealthStateState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Single health
Boolean alive
Byte meatLeft
SocialMemoryState socialMemory
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.SingleFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.SocialMemoryStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.CreatureStateState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean alive
Byte meatLeft
SocialMemoryState socialMemory
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.SocialMemoryStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.EntityFeedState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:No delta support for type: EntityFeedState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:EntityState entityState
OnlineResource inResource
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntity+EntityStateFalseTrueFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineResourceFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.GraspRef
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:No delta support for type: GraspRef
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 6 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte graspUsed
EntityId onlineGrabbed
Byte chunkGrabbed
Byte shareability
Single dominance
Boolean pacifying
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.PlayerNPCStateState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean Malnourished
Int32 foodInStomach
Int32 quarterFoodPoints
Boolean isPup
Single permanentDamageTracking
EntityId objectInStomach
Boolean alive
Byte meatLeft
SocialMemoryState socialMemory
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.SocialMemoryStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.PlayerStateState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 8 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Int32 foodInStomach
Int32 quarterFoodPoints
Boolean isPup
Single permanentDamageTracking
EntityId objectInStomach
Boolean alive
Byte meatLeft
SocialMemoryState socialMemory
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.SocialMemoryStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedCreatureState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 7 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.DaddyTentacleState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:No delta support for type: DaddyTentacleState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 5 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Single health
Vector2 pos
Nullable`1 floatGrabDest
Task task
BodyChunkRef grabChunk
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Nullable`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for DaddyTentacle+TaskFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.BodyChunkRefTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedDaddyLongLegsState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:DaddyTentacleState[] tentacles
Vector2 moveDirection
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.DaddyTentacleState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedDangleFruitState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte bites
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedDeerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Vector2 moveDirection
Single flipDir
Single resting
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedDropBugState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:WorldCoordinate ceilingPos
WorldCoordinate stayAwayFromPos
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedEggBugState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 8 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:SByte eggsLeft
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedFlyState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte bites
Byte eaten
Vector2 dir
Nullable`1 burrowOrHangSpot
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedGarbageWormState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 6 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean extended
Byte hole
Boolean showAsAngry
Vector2 lookPoint
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.LimbState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Vector2 pos
Mode mode
Nullable`1 huntPos
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for Limb+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.LizLimbState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean reachingForTerrain
Vector2 pos
Mode mode
Nullable`1 huntPos
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for Limb+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedLizardState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Nullable`1 gripPoint
DynamicOrderedStates`1 limbState
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.LizLimbState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedOracleState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Vector2 lookPoint
DynamicOrderedEntityIDs mySwarmers
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedEntityIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedOracleSwarmerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte bites
Boolean floaty
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedOverseerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 15 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Int32 ownerIterator
Vector2 rootPos
IntVector2 rootTile
IntVector2 hoverTile
Vector2 lookAt
Mode mode
Single extended
OnlinePhysicalObject conversationPartner
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RWCustom.IntVector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RWCustom.IntVector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for Overseer+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlinePhysicalObjectTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedPhysicalObjectState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.VinePositionState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:UInt16 index
Single floatPos
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.PlayerInAntlersState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:OnlinePhysicalObject onlineDeer
Boolean dangle
OnlineAntlerPoint upperAntlerPoint
OnlineAntlerPoint lowerAntlerPoint
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.PlayerInAntlersState+OnlineAntlerPointTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.PlayerInAntlersState+OnlineAntlerPointTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedPlayerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 29 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean malnourished
VinePositionState vinePosState
PlayerInAntlersState playerInAntlersState
Byte animationIndex
Int16 animationFrame
Byte bodyModeIndex
Boolean standing
Boolean flipDirection
Boolean glowing
Boolean afkSleep
EntityId spearOnBack
EntityId slugcatRidingOnBack
UInt16 inputs
Single analogInputX
Single analogInputY
Single burstX
Single burstY
Boolean monkAscension
TongueState tongueState
TongueState tongueStateOnBack
Boolean isCamo
Nullable`1 pointingDir
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 6 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.VinePositionStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.PlayerInAntlersStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.TongueStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.TongueStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedScavengerBombState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean explosionIsForShow
Boolean ignited
Mode mode
Single rotation
Single rotationSpeed
OnlineCreature thrownBy
Byte throwDir
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for Weapon+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineCreatureTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedScavengerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 12 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Nullable`1 swingPos
Single swingRadius
Single flip
Byte swingClimbCounter
Byte swingArm
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedSingularityBombState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean ignited
Boolean activateSingularity
Boolean activateSucktion
Byte counter
Mode mode
Single rotation
Single rotationSpeed
OnlineCreature thrownBy
Byte throwDir
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for Weapon+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineCreatureTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedSlimeMoldState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte bites
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedSLOracleState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 7 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:OnlinePhysicalObject holdingObject
Phase revivePhase
Int32 inPhaseCounter
Vector2 lookPoint
DynamicOrderedEntityIDs mySwarmers
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for SLOracleWakeUpProcedure+PhaseFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedEntityIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedSLOracleSwarmerState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 6 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean blackMode
Boolean hoverAtGrabablePos
Byte bites
Boolean floaty
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedSpearState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 16 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Nullable`1 stuckInWall
BodyChunkRef stuckInChunk
AppendageRef stuckInAppendage
SByte stuckBodyPart
Single stuckRotation
SByte stuckInWallCycles
Boolean needleActive
Single spearDamageBonus
Boolean ignited
Mode mode
Single rotation
Single rotationSpeed
OnlineCreature thrownBy
Byte throwDir
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.BodyChunkRefTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.AppendageRefTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for Weapon+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineCreatureTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedSporePlantState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 16 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Single angry
Int32 releaseBeesCounter
Int32 releaseBeesDelay
List`1 possibleDestinations
Boolean hasStalk
Vector2 stalkDirVec
Vector2 baseDirVec
Vector2 stalkStuckPos
Single coil
Mode mode
Single rotation
Single rotationSpeed
OnlineCreature thrownBy
Byte throwDir
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.List`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.List`1[RWCustom.IntVector2] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for Weapon+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineCreatureTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedTentaclePlantState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:OnlinePhysicalObject mostInterestingItem
Vector2 idlePos
Nullable`1 floatGrabDest
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedTubeWormState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 12 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:TongueState tongue0
TongueState tongue1
Single goalOnRopePos
Single onRopePos
Boolean sleeping
DynamicOrderedStates`1 grasps
Int16 stun
Nullable`1 enteringShortcut
WorldCoordinate transportationDestination
ArtificialIntelligenceState artificialIntelligenceState
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.TongueStateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.TongueStateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.GraspRef, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Nullable`1[[RWCustom.IntVector2, Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ArtificialIntelligenceStateTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedVultureGrubState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte bites
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.RealizedWeaponState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 7 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Mode mode
Single rotation
Single rotationSpeed
OnlineCreature thrownBy
Byte throwDir
ChunkState[] chunkStates
Byte collisionLayer
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for Weapon+ModeFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineCreatureTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.ChunkState[]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.TongueState
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 5 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Byte mode
Vector2 pos
Single idealRopeLength
Single requestedRopeLength
BodyChunkRef attachedChunk
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.BodyChunkRefTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.ArenaLobbyData+State
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 40 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean isInGame
Boolean hasPermissionToRejoin
Boolean allPlayersReadyLockLobby
Boolean returnToLobby
Dictionary`2 onlineArenaSettingsInterfaceMultiChoice
Dictionary`2 onlineArenaSettingsInterfaceBool
Dictionary`2 playersChoosingSlugs
Dictionary`2 playerResultColors
DynamicOrderedPlayerIDs playersReadiedUp
List`1 playList
Boolean shufflePlayList
Int32 totalLevels
Int32 arenaSetupTime
Int32 lobbyCountDown
Boolean initiatedLobbyCountDown
Int32 saintAscendanceTimer
Int32 watcherCamoLimit
Boolean sainot
Boolean painCatEgg
Boolean painCatThrows
Boolean painCatLizard
Boolean disableMaul
Boolean disableArtiStun
String currentGameMode
Boolean arenaItemSteal
Boolean allowJoiningMidRound
List`1 arenaSittingOnlineOrder
List`1 playersLateWaitingInLobby
DynamicOrderedPlayerIDs reigningChamps
Int32 currentLevel
Dictionary`2 playerNumberWithScore
Dictionary`2 playerNumberWithDeaths
Dictionary`2 playerNumberWithWins
Dictionary`2 playerNumberWithKills
Dictionary`2 playerTotScore
Boolean countdownInitiatedHoldFire
Int32 playerEnteredGame
Boolean leaveForNextLevel
Boolean weaponCollisionFix
Boolean playersEqualToOnlineSitting
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.Dictionary`2[System.String,System.Int32] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.Dictionary`2[System.String,System.Boolean] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedPlayerIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.List`1[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.List`1[System.String] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.List`1[[System.UInt16, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.List`1[System.UInt16] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.List`1[[System.UInt16, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedPlayerIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.Dictionary`2[System.Int32,System.Int32] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.TeamBattleLobbyData+State
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 15 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Single lerp
Int32 winningTeam
Int32 martyrs
Int32 outlaws
Int32 dragonslayers
Int32 chieftains
String martyrsName
String chieftainsName
String dragonslayersName
String outlawsName
Color martyrColors
Color chieftainColors
Color dragonslayerColors
Color outlawColors
Int32 roundSpawnPointCycler
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.ArenaTeamClientSettings+State
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Int32 team
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.ArenaClientSettings+State
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 6 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Name playingAs
Name randomPlayingAs
Color slugcatColor
Boolean selectingSlugcat
Boolean ready
Boolean gotSlugcat
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for SlugcatStats+NameFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for SlugcatStats+NameTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.ClientSettings+Definition
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.ClientSettings+State
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 5 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Boolean inGame
DynamicOrderedEntityIDs avatars
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedEntityIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:Registering RainMeadow.MeadowMusicData+State
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 2 fields
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:String providedSong
Single startedPlayingAt
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:03|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseFalseFalse
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:03|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.SlugcatCustomization+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 5 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Color[] customColors
Name playingAs
String nickname
Boolean wearingCape
Int32 playerIndex
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for SlugcatStats+NameTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.StoryClientSettingsData+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean readyForWin
Boolean readyForTransition
Boolean isDead
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.MeadowAvatarData+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Skin skin
Byte tintAmount
Color tint
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.MeadowProgression+SkinFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.MeadowCreatureData+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:DynamicOrderedExtEnums`1 emotes
TickReference emotesTick
Single emotesLife
Byte emotesVersion
UInt16 inputs
Single analogInputX
Single analogInputY
SpecialInput specialInput
WorldCoordinate destination
Single moveSpeed
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedExtEnums`1[[RainMeadow.MeadowProgression+Emote, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.TickReferenceFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.CreatureController+SpecialInputFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.MeadowLobbyData+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:FixedOrderedUshorts regionRedTokensGoal
FixedOrderedUshorts regionBlueTokensGoal
FixedOrderedUshorts regionGoldTokensGoal
FixedOrderedUshorts regionGhostsGoal
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.FixedOrderedUshortsFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.FixedOrderedUshortsFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.FixedOrderedUshortsFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.FixedOrderedUshortsFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineBubbleGrass+OnlineBubbleGrassDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int16 originRoom
SByte placedObjectIndex
Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineBubbleGrass+OnlineBubbleGrassState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Single oxygenLeft
Boolean isConsumed
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineConsumable+OnlineConsumableDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int16 originRoom
SByte placedObjectIndex
Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineConsumable+OnlineConsumableState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean isConsumed
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineCreature+OnlineCreatureDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:omitting fields: apoType
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 9 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Type creatureType
Boolean isAvatar
Int32 apoId
UInt16 apoSpawn
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for CreatureTemplate+TypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineMeadowCollectible+Definition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 8 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineMeadowCollectible+MeadowCollectibleState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean placed
Boolean collected
TickReference collectedTR
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.TickReferenceTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineMeadowCollectible+MeadowGhostState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 12 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Byte currentCount
Boolean placed
Boolean collected
TickReference collectedTR
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.TickReferenceTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlinePebblesPearl+OnlinePebblesPearlDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 12 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:SByte originalColor
Int16 originalNumber
Int16 originRoom
SByte placedObjectIndex
Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlinePhysicalObject+OnlinePhysicalObjectDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 8 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineSeedCob+OnlineSeedCobDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean originallyDead
Int16 originRoom
SByte placedObjectIndex
Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineSeedCob+OnlineSeedCobState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean opened
Boolean spawnedUtility
Boolean isConsumed
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineSpear+OnlineSpearDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean explosive
Boolean electric
Boolean needle
Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineSpear+OnlineSpearState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:SByte stuckInWallCycles
Byte electricCharge
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineSporePlant+OnlineSporePlantDefinition
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 10 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int16 originRoom
SByte placedObjectIndex
Int32 apoId
UInt16 apoSpawn
AbstractObjectType apoType
String extraData
EntityId entityId
UInt16 owner
Boolean isTransferable
UInt16 version
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.OnlineSporePlant+OnlineSporePlantState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 11 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Boolean pacified
Boolean used
Boolean isConsumed
WorldCoordinate pos
Boolean inDen
DynamicOrderedStates`1 sticks
Boolean realized
RealizedPhysicalObjectState realizedObjectState
EntityId entityId
UInt16 version
DeltaDataStates`1 entityDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedStates`1[[RainMeadow.AbstractObjStickRepr, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineEntity+EntityData+EntityDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RealizedPhysicalObjectStateTrueTrueFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.Lobby+LobbyState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 15 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:UInt16 nextId
String[] requiredmods
String[] bannedmods
DynamicOrderedPlayerIDs bannedUsers
DynamicOrderedPlayerIDs players
DynamicOrderedUshorts inLobbyIds
Dictionary`2 onlineBoolRemixSettings
Dictionary`2 onlineFloatRemixSettings
Dictionary`2 onlineIntRemixSettings
LeaseList subleaseState
OnlineResource resource
DynamicIdentifiablesICustomSerializables`2 entitiesJoined
DeltaStates`2 registeredEntities
DeltaStates`2 entityStates
DeltaDataStates`1 resourceDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 4 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineResourceFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.UInt16FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.String[]FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.String[] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.String[]FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedPlayerIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedPlayerIDsTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicOrderedUshortsTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.MakeSerializationMethod:System.Collections.Generic.Dictionary`2[System.String,System.Single] not handled by SerializerCallMethod
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineResource+LeaseListTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DynamicIdentifiablesICustomSerializables`2[[RainMeadow.EntityMembership, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DeltaStates`2[[RainMeadow.OnlineEntity+EntityDefinition, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DeltaStates`2[[RainMeadow.OnlineEntity+EntityState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineResource+ResourceData+ResourceDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.RoomSession+RoomState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 5 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:OnlineResource resource
DynamicIdentifiablesICustomSerializables`2 entitiesJoined
DeltaStates`2 registeredEntities
DeltaStates`2 entityStates
DeltaDataStates`1 resourceDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineResourceFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicIdentifiablesICustomSerializables`2[[RainMeadow.EntityMembership, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaStates`2[[RainMeadow.OnlineEntity+EntityDefinition, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaStates`2[[RainMeadow.OnlineEntity+EntityState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineResource+ResourceData+ResourceDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.WorldSession+WorldState
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 8 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:RainCycleData rainCycleData
DynamicOrderedUshorts realizedRooms
LeaseList subleaseState
OnlineResource resource
DynamicIdentifiablesICustomSerializables`2 entitiesJoined
DeltaStates`2 registeredEntities
DeltaStates`2 entityStates
DeltaDataStates`1 resourceDataStates
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 4 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineResourceFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.WorldSession+RainCycleDataFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicOrderedUshortsTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineResource+LeaseListTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DynamicIdentifiablesICustomSerializables`2[[RainMeadow.EntityMembership, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaStates`2[[RainMeadow.OnlineEntity+EntityDefinition, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaStates`2[[RainMeadow.OnlineEntity+EntityState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null],[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.Generics.DeltaDataStates`1[[RainMeadow.OnlineResource+ResourceData+ResourceDataState, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]TrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.WorldSession+RainCycleData
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int32 cycleLength
Int32 timer
Int32 preTimer
Boolean antiGravity
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 2 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.AbstractObjStickRepr+SpearStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:No delta support for type: SpearStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 4 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:SByte chunk
SByte bodyPart
Single angle
EntityId B
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.AbstractObjStickRepr+SpearAppendageStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:No delta support for type: SpearAppendageStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 5 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:SByte appendage
SByte prevSeg
Single distanceToNext
Single angle
EntityId B
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.AbstractObjStickRepr+ImpaledOnSpearStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:No delta support for type: ImpaledOnSpearStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:SByte chunk
SByte onSpearPosition
EntityId B
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.AbstractObjStickRepr+OnBackStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:No delta support for type: OnBackStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:EntityId B
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.AbstractObjStickRepr+SlugcatOnBackStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:No delta support for type: SlugcatOnBackStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:EntityId B
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.AbstractObjStickRepr+CreatureGripStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:No delta support for type: CreatureGripStick
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:SByte grasp
Boolean carry
EntityId B
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.PlayerInAntlersState+OnlineAntlerPoint
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 3 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Int32 part
Int32 segment
Single side
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.StoryLobbyData+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 26 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:String defaultDenPos
String region
String defaultWarpPos
Boolean isInGame
Boolean changedRegions
Boolean readyForWin
Byte readyForTransition
Boolean friendlyFire
Name currentCampaign
Int32 cycleNumber
Boolean reinforcedKarma
Int32 karmaCap
Int32 karma
Boolean theGlow
Int32 food
Int32 quarterfood
Int32 mushroomCounter
String saveStateString
Boolean lastMalnourished
Boolean malnourished
Boolean requireCampaignSlugcat
List`1 pups
Boolean storyItemSteal
Single rippleLevel
Single minimumRippleLevel
Single maximumRippleLevel
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for SlugcatStats+NameFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Collections.Generic.List`1[[RainMeadow.OnlineEntity+EntityId, Rain Meadow, Version=*******, Culture=neutral, PublicKeyToken=null]]FalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:Registering RainMeadow.MeadowMusic+LobbyMusicData+State
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 2 fields
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:UshortToByteDict playerGroups
ByteToUshortDict groupHosts
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:found 1 groups
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.UshortToByteDictTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.Generics.ByteToUshortDictTrueFalseFalse
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:{var selfConverted;var Param_0;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|OnlineState..ctor:(self, Param_0) => {var selfConverted;var Param_1;var output; ... }
[Info   :RainMeadow] 00:01:04|1553|RainMeadow.RainWorld_OnModsInit:OnlineState.InitializeBuiltinTypes: 00:00:01.4309152
[Info   :RainMeadow] 00:01:04|1553|RainMeadow.RainWorld_OnModsInit:OnlineGameMode.InitializeBuiltinTypes: 00:00:00.0022628
[Info   :RainMeadow] 00:01:04|1553|MeadowProgression.InitializeBuiltinTypes:characters loaded: 7
[Info   :RainMeadow] 00:01:04|1553|MeadowProgression.InitializeBuiltinTypes:skins loaded: 49
[Info   :RainMeadow] 00:01:04|1553|MeadowProgression.InitializeBuiltinTypes:emotes loaded: 37
[Info   :RainMeadow] 00:01:04|1553|RainMeadow.RainWorld_OnModsInit:MeadowProgression.InitializeBuiltinTypes: 00:00:00.0112663
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_ForceReady
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_NotifySpawnPoint
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Int32 martyrs - Int32 outlaws - Int32 dragonslayers - Int32 chieftains
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_RemovePlayerWhoQuit
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePlayer earlyQuitterOrLatecomer
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlinePlayerTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_AddPlayerWaiting
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePlayer earlyQuitterOrLatecomer
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePlayerTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_NotifyRejoinAllowed
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Boolean hasPermission
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_EndSessionEarly
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_ForceReadyUp
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_NotifyClassChange
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePlayer userChangingClass - Int32 currentColorIndex
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePlayerTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_UpdateSelectedChoice
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String stringID - Int32 value
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_UpdateSelectedCheckbox
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String stringID - Boolean c
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_InitialSetupTimers
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Int32 setupTime - Int32 saintMaxTime
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_ReadyForNextLevel
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_AddTrophy
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject creatureKilled - Int32 playerNum
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_NotifyLobbyReadyUp
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePlayer userIsReady
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePlayerTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-AddShortCutVessel
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - RWCustom.IntVector2 pos - OnlinePhysicalObject crit - RoomSession roomSess - Int32 wait
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RWCustom.IntVector2FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.RoomSessionTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_LevelToPlaylist
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String chosenLevel
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ArenaRPCs-Arena_LevelFromPlaylist
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Int32 index - System.String chosenLevel
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowGameMode-ItemConsumed
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Byte region - AbstractPhysicalObject+AbstractObjectType type
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.ByteFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for AbstractPhysicalObject+AbstractObjectTypeTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowGameMode-SpawnItem
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Byte region - AbstractPhysicalObject+AbstractObjectType type
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for AbstractPhysicalObject+AbstractObjectTypeTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.AbstractMeadowCollectible-CollectRemote
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject online
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowMusic-AskNowLeave
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowMusic-BroadcastInterruption
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String song
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowMusic-InteruptSong
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String song
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowMusic-AskNowJoinPlayer
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePlayer other
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePlayerTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.MeadowMusic-AskNowSquashPlayers
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - UInt16[] playersinquestion
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.UInt16[]FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ConsumableRPCs-pacifySporePlant
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject onlineSporePlant
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ConsumableRPCs-enableTheGlow
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.ConsumableRPCs-reportConsumedItem
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Boolean karmaFlower - Int32 originroom - Int32 placedObjectIndex - Int32 waitCycles
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineCreature-CreatureViolence
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject onlineVillain - Byte victimChunkIndex - AppendageRef victimAppendageRef - System.Nullable`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]] directionAndMomentum - Creature+DamageType damageType - Single damage - Single stunBonus
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.ByteFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.AppendageRefTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Nullable`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]TrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for Creature+DamageTypeTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.SingleFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineCreature-HopOnBack
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlineCreature uppyWanter
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineCreatureTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineCreature-SuckedIntoShortCut
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - RWCustom.IntVector2 entrancePos - Boolean carriedByOther - Boolean sucked_in_by_remote
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RWCustom.IntVector2FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineCreature-SpitOutOfShortCut
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - RWCustom.IntVector2 pos - RoomSession newRoom - Boolean spitOutAllSticks
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RWCustom.IntVector2FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.RoomSessionTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineCreature-TookFlight
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - AbstractRoomNode+Type type - WorldCoordinate start - WorldCoordinate dest
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for AbstractRoomNode+TypeTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for WorldCoordinateFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for WorldCoordinateFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineEntity-Requested
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineEntity-Released
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlineResource inResource
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineResourceTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlinePhysicalObject-WeaponHitSomething
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - RealizedWeaponState statewhenhit - OnlineCollisionResult hit
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.RealizedWeaponStateTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlinePhysicalObject+OnlineCollisionResultTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlinePhysicalObject-HitByExplosion
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Single hitfac
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlinePhysicalObject-Trigger
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlinePhysicalObject-Explode
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - UnityEngine.Vector2 pos
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineEntity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for UnityEngine.Vector2FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.Lobby-RequestedLobby
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String key
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-OnEntityRegisterRequest
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - EntityDefinition newEntityEvent - EntityState initialState
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntity+EntityDefinitionTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntity+EntityStateTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-OnEntityJoinRequest
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlineEntity oe - EntityState initialState
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntityTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityStateTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-OnEntityLeaveRequest
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlineEntity oe
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntityTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-OnEntityTransferRequest
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlineEntity oe - OnlinePlayer newOwner
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntityTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePlayerTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-Requested
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-Released
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.OnlineResource-Transfered
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RoomSession-AbstractRoomFirstTimeRealized
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RoomSession-CreaturePutItemOnGround
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - EntityId item - EntityId creature
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:target is OnlineResource
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for RainMeadow.OnlineEntity+EntityIdTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-DeltaReset
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlineResource onlineResource - EntityId entity
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineResourceTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlineEntity+EntityIdTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-UpdateUsernameTemporarily
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String lastSentMessage
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-KickToLobby
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-ExitToGameModeMenu
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-Creature_Die
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject opo - OnlinePhysicalObject saint
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-KillFeedEnvironment
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject opo - Int32 index
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-KillFeedPvP
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject killer - OnlinePhysicalObject target - Int32 context
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.RPCs-KillFeedCvP
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - OnlinePhysicalObject killer - OnlinePhysicalObject target
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for RainMeadow.OnlinePhysicalObjectTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-ForceSaveNewDenLocation
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String shelter - Boolean saveWorldStates
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-ChangeFood
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Int16 amt
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for System.Int16FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-AddMushroomCounter
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-ReinforceKarma
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-PlayReinforceKarmaAnimation
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToWinScreen
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Boolean malnourished - Boolean fromWarpPoint - System.String denPos - System.String warpPointTarget
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToStarveScreen
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String denPos
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToGhostScreen
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - GhostWorldPresence+GhostID ghostID
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for GhostWorldPresence+GhostIDTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToDeathScreen
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToPassageScreen
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - WinState+EndgameID endGameID
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Adding cached method for WinState+EndgameIDTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToRedsGameOver
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToRivuletEnding
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-GoToSpearmasterEnding
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-RaiseRippleLevel
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - UnityEngine.Vector2 vector
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-PlayRaiseRippleLevelAnimation
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - UnityEngine.Vector2 vector
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for UnityEngine.Vector2FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-NormalExecuteWatcherRiftWarp
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String sourceRoomName - System.String warpData - Boolean useNormalWarpLoader
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-EchoExecuteWatcherRiftWarp
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String sourceRoomName - System.String warpData
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-InfectRegionRoomWithSentientRot
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Single amount - System.String roomName
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.SingleFalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-PrinceSetHighestConversation
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - Int32 newValue
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.Int32FalseTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-TriggerGhostHunch
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs: - System.String ghostID
[Info   :RainMeadow] 00:01:04|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.StringTrueTrueTrue
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-LC_FINAL_TriggerFadeToEnding
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-RegionGateOrWarpMeetRequirement
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:New RPC: RainMeadow.StoryRPCs-CloseAllShelters
[Info   :RainMeadow] 00:01:04|1553|RPCManager.RegisterRPCs:
[Info   :RainMeadow] 00:01:10|1553|RPCManager.RegisterRPCs:New RPC: meadowMeow.OnlineMeow-Meow
[Info   :RainMeadow] 00:01:10|1553|RPCManager.RegisterRPCs: - Boolean isShortMeow
[Info   :RainMeadow] 00:01:10|1553|Serializer.ICustomSerializable.GetSerializationMethod:Using cached method for System.BooleanFalseTrueTrue
[Info   :RainMeadow] 00:01:11|1553|RainMeadow.RainWorld_OnModsInit:RPCManager.SetupRPCs: 00:00:06.9894046
[Info   :RainMeadow] 00:01:11|1553|RainMeadow.RainWorld_OnModsInit:found shader RM_LeveIltem
[Info   :RainMeadow] 00:01:11|1553|RainMeadow.RainWorld_OnModsInit:registered as new shader
[Info   :RainMeadow] 00:01:11|1553|RainMeadow.RainWorld_OnModsInit:found shader RM_SceneHidden
[Info   :RainMeadow] 00:01:11|1553|RainMeadow.RainWorld_OnModsInit:registered as new shader
[Info   :RainMeadow] 00:01:15|1553|RainMeadow.PlayerHooks.GourmandOnBackMechanics:Overriden 1 comparisons in Player::SlugSlamConditions
[Info   :RainMeadow] 00:01:15|1553|RainMeadow.PlayerHooks.GourmandOnBackMechanics:Overriden 1 comparisons in Player::ClassMechanicsGourmand
[Info   :RainMeadow] Increased player count: Method at ArenaGameSession::.ctor]
[Info   :RainMeadow] Increased player count: Method at CreatureCommunities::.ctor]
[Info   :RainMeadow] 00:01:21|1553|ArenaHooks.Player_Collide2:Gourm Stomp RPC set with Player
[Info   :RainMeadow] 00:01:21|1553|ArenaHooks.Player_Collide2:Gourm Stomp RPC set with Player
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SU_C04StartUp_Update:Replacec 15 SU_C04 physical object references in 0.0020264s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.CharacterSelectPage::.ctor in 0.0004497s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.CharacterSelectPage::SetUpSelectables in 0.0002211s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.CharacterSelectPage::Update in 0.0001952s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.CharacterSelectPage::UpdateSelectedSlugcat in 0.0002233s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.InitializationScreen::Update in 0.0010394s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.MainMenu::.ctor in 0.0003247s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.SlugcatSelectMenu::AddColorInterface in 0.0002134s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.SlugcatSelectMenu::.ctor in 0.0002967s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 9 Jolly CoOP checks in Menu.SlugcatSelectMenu::Update in 0.0009449s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in PlayerProgression::SaveToDisk in 0.0014331s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerProgression::SyncLoadModState in 0.0002237s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in AbstractCreature::IsEnteringDen in 0.0001181s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in AbstractCreature::RealizeInRoom in 0.0001641s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Creature::Update in 0.00026s
[Info   :RainMeadow] DeathContextualizer binded with DaddyCorruption+EatenCreature
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in DaddyCorruption+EatenCreature::Update in 0.0001511s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Ghost::Update in 0.0002117s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in HardmodeStart::.ctor in 0.0008662s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in HardmodeStart::Update in 0.0001953s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in HUD.HUD::InitSinglePlayerHud in 0.0016001s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in HUD.TextPrompt::EnterGameOverMode in 0.0001617s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in HUD.TextPrompt::Update in 0.0002142s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in ImageTrigger::AttemptTriggerFire in 0.0001767s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in KarmaFlower::BitByPlayer in 0.0001853s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in LizardAI::IUseARelationshipTracker.UpdateDynamicRelationship in 0.0002012s
[Info   :RainMeadow] 00:01:22|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.ChallengeSelectPage::StartGame in 0.0001463s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.CharacterSelectPage::LoadGame in 0.0002025s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.ControlMap::.ctor in 0.0003714s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.ExpeditionGameOver::Singal in 0.0001577s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.ExpeditionMenu::CommunicateWithUpcomingProcess in 0.000197s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.InputOptionsMenu::Singal in 0.0001349s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.SlugcatSelectMenu::CommunicateWithUpcomingProcess in 0.0001963s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.SlugcatSelectMenu::StartGame in 0.0001716s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 9 Jolly CoOP checks in Menu.SlugcatSelectMenu::Update in 0.0007674s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 9 Jolly CoOP checks in Menu.SlugcatSelectMenu::Update in 0.000627s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in MoreSlugcats.BreathMeter::Update in 0.0002711s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in MoreSlugcats.MSCRoomSpecificScript+GW_C05ArtificerMessage::Update in 0.0004959s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in MoreSlugcats.MSCRoomSpecificScript+OE_GourmandEnding::Update in 0.0012852s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in MoreSlugcats.MSCRoomSpecificScript+SU_A42Message::Update in 0.0001644s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in MoreSlugcats.MSCRoomSpecificScript+SU_PMPSTATION01_safety::Update in 0.0001333s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in MoreSlugcats.MSCRoomSpecificScript+SU_SMIntroMessage::Update in 0.000301s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in OracleBehavior::FindPlayer in 0.0001387s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in OracleBehavior::.ctor in 0.0001409s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in Player::AddFood in 0.0002097s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in Player::AddQuarterFood in 0.0001848s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in Player::SubtractFood in 0.0002554s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::CanEatMeat in 0.0003646s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in Player::CanIPickThisUp in 0.000224s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::CanIPutDeadSlugOnBack in 0.0002541s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::CanMaulCreature in 0.0004018s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in Player::checkInput in 0.0002582s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::ClassMechanicsArtificer in 0.0001858s
[Info   :RainMeadow] 00:01:23|1553|ArenaHooks.Player_Collide2:Gourm Stomp RPC set with Player
[Info   :RainMeadow] 00:01:23|1553|ArenaHooks.Player_Collide2:Gourm Stomp RPC set with Player
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::Collide in 0.0002321s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::Destroy in 0.0001546s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 5 Jolly CoOP checks in Player::Die in 0.0025112s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::GetHeldItemDirection in 0.0001983s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::GetInitialSlugcatClass in 0.0001795s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::get_RevealMap in 0.0003227s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::get_slugcatStats in 0.0001808s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::Grabability in 0.0001718s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::GrabUpdate in 0.0012606s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::GraphicsModuleUpdated in 0.0001797s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::HeavyCarry in 0.0001867s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::JollyUpdate in 0.0001606s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::ObjectEaten in 0.0001674s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::PermaDie in 0.000221s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in Player::.ctor in 0.000217s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::ProcessChatLog in 0.0001514s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::PyroDeathThreshold in 0.0002308s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::SaveStomachObjectInPlayerState in 0.0001781s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::SetMalnourished in 0.0004404s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::SlugcatGrab in 0.0006915s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player+SlugOnBack::GraphicsModuleUpdated in 0.0001244s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player+SlugOnBack::SlugToBack in 0.0006256s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player+SlugOnBack::Update in 0.0003744s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.PlayerHooks.GourmandOnBackMechanics:Overriden 1 comparisons in Player::SlugSlamConditions
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::SlugSlamConditions in 0.0001096s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::Stun in 0.0001517s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::Update in 0.0010052s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::UpdateMSC in 0.0002011s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in PlayerGraphics::ApplyPalette in 0.0001708s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics+CosmeticPearl::DrawSprites in 0.0001634s
[Info   :RainMeadow] 00:01:23|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::CustomColorsEnabled in 0.0001709s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::DrawSprites in 0.000606s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::get_useJollyColor in 0.0001928s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::MSCUpdate in 0.0002026s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::.ctor in 0.0003331s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in PlayerGraphics+TailSpeckles::DrawSprites in 0.0008766s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in ProcessManager::IsGameInMultiplayerContext in 0.0002358s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorld::Update in 0.00017s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::AppendCycleToStatisticsForPlayers in 0.0001474s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::BeatGameMode in 0.0005811s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::CommunicateWithUpcomingProcess in 0.0003004s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::GameOver in 0.0002235s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::GoToRedsGameOver in 0.0001918s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::InActiveGate in 0.0001353s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RainWorldGame::InClosingShelter in 0.0002181s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 3 Jolly CoOP checks in RainWorldGame::.ctor in 0.0004063s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in RainWorldGame::SpawnPlayers in 0.0002411s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RedsIllness::.ctor in 0.0001903s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RegionGate::ListOfPlayersInZone in 0.0001382s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RegionGate::PlayersInZone in 0.0005068s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RegionGate::PlayersStandingStill in 0.0001546s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RoomCamera::EnterCutsceneMode in 0.0001607s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RoomCamera::Update in 0.0003067s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RoomRealizer::CanAbstractizeRoom in 0.000149s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in RoomSpecificScript+SB_A14KarmaIncrease::Update in 0.0002044s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in RoomSpecificScript+SL_C12JetFish::.ctor in 0.0001993s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SU_C04StartUp_Update:Replacec 15 SU_C04 physical object references in 0.0009917s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in RoomSpecificScript+SU_C04StartUp::Update in 0.0001219s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.WhiteListJollyCoop:Replace 1 Jolly CoOP checks in ShelterDoor::DoorClosed in 0.0002591s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in SaveState::BringUpToDate in 0.0002382s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 4 Jolly CoOP checks in SaveState::SessionEnded in 0.0004526s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in ShelterDoor::Close in 0.0001744s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in ShelterDoor::Update in 0.0004655s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in ShortcutHandler::SuckInCreature in 8.76E-05s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in ShortcutHandler::Update in 0.0001317s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in SLOracleBehavior::Update in 0.0001629s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in SlugcatHand::Update in 0.0002481s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Spear::DrawSprites in 0.0001599s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Spear::HitSomethingWithoutStopping in 0.0009902s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in SSOracleBehavior::SeePlayer in 0.0002827s
[Info   :RainMeadow] 00:01:24|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in SSOracleBehavior+SSOracleGetGreenNeuron::Update in 0.0002085s
[Info   :RainMeadow] DeathContextualizer binded with SSOracleBehavior+ThrowOutBehavior
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in SSOracleBehavior+ThrowOutBehavior::Update in 0.0003262s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 6 Jolly CoOP checks in SSOracleBehavior::Update in 0.0012001s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in StoryGameSession::PlaceKarmaFlowerOnDeathSpot in 0.0001749s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in StoryGameSession::TimeTick in 0.0001989s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidSeaScene::DestroyMainWorm in 0.0001582s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidSeaScene::MovedToSecondSpace in 0.0001863s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidSeaScene+TheEgg::DrawSprites in 0.0001789s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidSeaScene+TheEgg::Update in 0.0002102s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 4 Jolly CoOP checks in VoidSea.VoidSeaScene::Update in 0.0004134s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidSeaScene+WormLightFade::DrawSprites in 0.0002044s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidWorm+Arm::Update in 0.0003643s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidWorm+BackgroundWormBehavior::Update in 0.0001867s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidWorm+Head::Update in 0.0001803s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidWorm+MainWormBehavior::Update in 0.0006036s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in VoidSea.VoidWorm::Update in 0.0002516s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Vulture::AccessSkyGate in 0.0001865s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Weapon::HitThisObject in 0.000184s
[Info   :RainMeadow] 00:01:25|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in WormGrass+WormGrassPatch::InteractWithCreature in 0.0001901s
[Info   :RainMeadow] 00:01:26|1553|SlugcatCape.getRemoteLatestCommit:0000015bb7787a1e76c7455c53c327dc3c4711bb4710739e HEAD multi_ack thin-pack side-band side-band-64k ofs-delta shallow deepen-since deepen-not deepen-relative no-progress include-tag multi_ack_detailed allow-tip-sha1-in-want allow-reachable-sha1-in-want no-done symref=HEAD:refs/heads/master filter object-format=sha1 agent=git/github-5a2d4c40a633-Linux
[Info   :RainMeadow] 00:01:26|1553|SlugcatCape.getRemoteLatestCommit:recieved the hash latest commit: 015bb7787a1e76c7455c53c327dc3c4711bb4710739e
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone CC_C07,1100,4400,Sine in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Woodback in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Well Phoe in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cloudlayer in CC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone CL_AI,1000,1666,Sine in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Woodback in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in CL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone DM_O02,1600,6000,Litri in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Well Phoe in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in DM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone DS_B03,1000,2700,Clar in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Triptrap X in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in DS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone GW_C09,666,3000,Bell in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Triptrap X in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cloudlayer in GW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone HI_C04,1100,3000,Trisaw in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Woodback in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Well Phoe in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ire lining in HI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Triptrap X in HR
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone LC_FINAL,900,3000,Clar in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Triptrap X in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Sum Picaroon in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in LC
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone LF_D06,2600,4600,Clar in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Woodback in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Triptrap X in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ire lining in LF
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone LM_B01,800,1800,Bell in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Well Phoe in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Smoothed Ash in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ire lining in LM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone MS_COMMS,900,2400,Trisaw in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 403rings in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Sum Picaroon in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ire lining in MS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone OE_TREETOP,2800,4800,Sine in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Woodback in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cascen in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Gray Orange in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone OE_TOWER06,1000,2400,Litri in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in OE
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Smoothed Ash in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in RM
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone SB_B02,1000,3600,Litri in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Smoothed Ash in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Sum Picaroon in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in SB
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone SH_C07,1200,4000,Bell in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Well Phoe in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Smoothed Ash in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in SH
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone SI_D01,900,3000,Sine in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cascen in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Gray Orange in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cloudlayer in SI
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone SL_E01,1200,4000,Litri in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ones in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Gray Orange in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Porls in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Smoothed Ash in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song New and new in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Sum Picaroon in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ire lining in SL
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone SS_I03,1300,6000,Litri in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Significance in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Well Phoe in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song The Crewmate in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song my in SS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone SU_B09,666,3000,Trisaw in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyes' Vain in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cascen in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Gray Orange in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Pedal Petal in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in SU
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone UG_B03,1000,3200,Clar in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cascen in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Gray Orange in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Void Genesis in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Grasp in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Smoothed Ash in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song slow in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cloudlayer in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Ire lining in UG
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone UW_J02,1200,6660,Clar in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Eyto in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Gray Orange in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Icy Parchment in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song tredjeplanen in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Slightly Ill in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song 71104 in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song DustAshWrong in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Sum Picaroon in UW
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered vibezone VS_F01,1300,3500,Litri in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Indufor in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Me in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Folkada in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Trists in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Walked in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Soup in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Live more. in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song MTC in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Nevertop Side in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Purple Puff in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Swan ode in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Triptrap X in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Honeydew Chains in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Afio in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowMusic.CheckFiles:Meadow Music: Registered song Cloudlayer in VS
[Info   :RainMeadow] 00:01:26|1553|MeadowProgression.LoadProgression
[Info   :RainMeadow] 00:01:26|1553|SteamMatchmakingManager..ctor
[Info   :RainMeadow] 00:01:26|1553|SteamMatchmakingManager.LeaveLobby
[Info   :RainMeadow] 00:01:26|1553|RainMeadowModManager.Reset:Restoring config settings
[Info   :RainMeadow] 00:01:27|1553|SteamMatchmakingManager.initializeMePlayer
[Info   :RainMeadow] 00:01:27|1553|SteamMatchmakingManager.LeaveLobby
[Info   :RainMeadow] 00:01:27|1553|RainMeadowModManager.Reset:Restoring config settings
[Info   :RainMeadow] 00:01:27|1553|SteamMatchmakingManager.initializeMePlayer
[Info   :RainMeadow] 00:01:27|1553|OnlineManager..ctor:OnlineManager Created
[Info   :RainMeadow] 00:01:27|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::GrabUpdate in 0.0011381s
[Info   :RainMeadow] 00:01:27|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::GrabUpdate in 0.0009576s
[Info   :RainMeadow] 00:01:27|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 6 Jolly CoOP checks in SSOracleBehavior::Update in 0.0007787s
[Info   :RainMeadow] 00:01:27|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.SlugcatSelectMenu::StartGame in 0.000177s
[Info   :RainMeadow] 00:01:28|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::GrabUpdate in 0.0009993s
[Info   :RainMeadow] 00:01:28|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in PlayerGraphics::ApplyPalette in 0.0003119s
[Info   :RainMeadow] 00:01:29|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Menu.CharacterSelectPage::.ctor in 0.0002348s
[Info   :RainMeadow] 00:01:29|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::DrawSprites in 0.0004302s
[Info   :RainMeadow] 00:01:29|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in PlayerGraphics::DrawSprites in 0.00034s
[Debug  :Music Volume Adjuster] File found.
[Debug  :Music Volume Adjuster] Loading VolumeData...
[Debug  :Music Volume Adjuster] Loading VolumeData Complete
[Debug  :Music Volume Adjuster] Loaded VolumeData: Threat:100, music100
[Message: RegionKit] Registering module Assets
[Message: RegionKit] Registering module Common Hooks
[Message: RegionKit] Registering module CustomSSSong
[Message: RegionKit] Registering module RoomLoader
[Message: RegionKit] Registering module The Mast
[Message: RegionKit] Registering module Shelter Behaviors
[Message: RegionKit] Registering module Shader Tools
[Message: RegionKit] Registering module Room zones
[Message: RegionKit] Registering module Room Slideshow
[Message: RegionKit] Registering module Particles
[Message: RegionKit] Registering module MiscObjects
[Message: RegionKit] Registering module Multi-Color Snow
[Message: RegionKit] Registering module Miscellanceous
[Message: RegionKit] Registering module Machinery
[Message: RegionKit] Registering module Insects
[Message: RegionKit] Registering module IndividualPlacedObjectViewer
[Message: RegionKit] Registering module Iggy
[Message: RegionKit] Registering module GateCustomization
[Message: RegionKit] Registering module Effects
[Message: RegionKit] Registering module Echo Extender
[Message: RegionKit] Registering module DevUI
[Message: RegionKit] Registering module CustomProjections
[Message: RegionKit] Registering module Concealed Garden
[Message: RegionKit] Registering module Climbables
[Message: RegionKit] Registering module BackgroundBuilder
[Message: RegionKit] Registering module AridBarrens
[Message: RegionKit] Registering module Animated Decals
[Debug  : RegionKit] Scanned RegionKit, Version=********, Culture=neutral, PublicKeyToken=null for modules in 00:00:00.0169611
[Debug  : RegionKit] setup Assets
[Debug  : RegionKit] enable Assets
[Message: RegionKit] Assets module loading atlases from assetpath assets/regionkit
[Debug  : RegionKit] assets/regionkit/clippy loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/bigkarmashrine loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/deviggy loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/extendedgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/fan loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/gatecustomization loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/littleplanet loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/littleplanetring loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/shortcutcannon loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/spiketip loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/symbol_pearlchain loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/tatgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/themast loading as atlas
[Debug  : RegionKit] Resources loaded in 00:00:00.2316706
[Debug  : RegionKit] setup Common Hooks
[Debug  : RegionKit] enable Common Hooks
[Debug  : RegionKit] setup CustomSSSong
[Debug  : RegionKit] enable CustomSSSong
[Info   :RainMeadow] Custom SS Music Loaded:

[Debug  : RegionKit] setup RoomLoader
[Debug  : RegionKit] enable RoomLoader
[Debug  : RegionKit] setup The Mast
[Debug  : RegionKit] enable The Mast
[Debug  : RegionKit] setup Shelter Behaviors
[Debug  : RegionKit] enable Shelter Behaviors
[Debug  : RegionKit] setup Shader Tools
[Debug  : RegionKit] enable Shader Tools
[Debug  : RegionKit] setup Room zones
[Debug  : RegionKit] enable Room zones
[Debug  : RegionKit] setup Room Slideshow
[Debug  : RegionKit] enable Room Slideshow
[Debug  : RegionKit] SetShader { InstantlyProgress = True, shader = Basic }, SetDelay { InstantlyProgress = True, newDelay = 40 }, SetInterpolation { InstantlyProgress = True, interpolator = RegionKit.Modules.RoomSlideShow.Interpolator, value = Linear, channels = RegionKit.Modules.RoomSlideShow.Channel[] }, SetInterpolation { InstantlyProgress = True, interpolator = RegionKit.Modules.RoomSlideShow.Interpolator, value = Quadratic, channels = RegionKit.Modules.RoomSlideShow.Channel[] }, SetContainer { InstantlyProgress = True, newContainer = Foreground }, Frame { InstantlyProgress = False, index = 5, elementName = LizardHead0.1, ticksDuration = 60, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 6, elementName = Circle20, ticksDuration = 40, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 7, elementName = LizardHead0.2, ticksDuration = 60, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 8, elementName = Circle20, ticksDuration = 40, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }, Frame { InstantlyProgress = False, index = 9, elementName = LizardHead0.1, ticksDuration = 60, keyFramesHere = RegionKit.Modules.RoomSlideShow.KeyFrame[] }
[Debug  : RegionKit] setup Particles
[Debug  : RegionKit] enable Particles
[Warning:RainMeadow] Found no particle presets
[Debug  : RegionKit] setup MiscObjects
[Debug  : RegionKit] enable MiscObjects
[Debug  :       Pom] ILHook body start
[Debug  :       Pom] Found inj point, emitting
[Debug  :       Pom] emit complete
[Debug  : RegionKit] setup Multi-Color Snow
[Debug  : RegionKit] enable Multi-Color Snow
[Info   :RainMeadow] 00:01:33|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in RoomCamera::Update in 0.0002713s
[Debug  : RegionKit] setup Miscellanceous
[Debug  : RegionKit] enable Miscellanceous
[Debug  : RegionKit] setup Machinery
[Debug  : RegionKit] enable Machinery
[Debug  : RegionKit] setup Insects
[Debug  : RegionKit] enable Insects
[Debug  : RegionKit] setup IndividualPlacedObjectViewer
[Debug  : RegionKit] enable IndividualPlacedObjectViewer
[Debug  : RegionKit] setup Iggy
[Debug  : RegionKit] enable Iggy
[Debug  : RegionKit] setup GateCustomization
[Debug  : RegionKit] enable GateCustomization
[Debug  : RegionKit] setup Effects
[Message: RegionKit] entered loading / loading status: False
[Debug  : RegionKit] enable Effects
[Debug  : RegionKit] setup Echo Extender
[Debug  : RegionKit] enable Echo Extender
[Debug  : RegionKit] setup DevUI
[Debug  : RegionKit] enable DevUI
[Debug  : RegionKit] setup CustomProjections
[Debug  : RegionKit] enable CustomProjections
[Debug  : RegionKit] setup Concealed Garden
[Debug  : RegionKit] enable Concealed Garden
[Debug  :       Pom] ILHook body start
[Debug  :       Pom] Found inj point, emitting
[Debug  :       Pom] emit complete
[Debug  : RegionKit] setup Climbables
[Debug  : RegionKit] enable Climbables
[Debug  : RegionKit] setup BackgroundBuilder
[Debug  : RegionKit] enable BackgroundBuilder
[Debug  : RegionKit] setup AridBarrens
[Debug  : RegionKit] enable AridBarrens
[Debug  : RegionKit] setup Animated Decals
[Debug  : RegionKit] enable Animated Decals
[Message: RegionKit] Assets module loading atlases from assetpath assets/regionkit
[Debug  : RegionKit] assets/regionkit/clippy loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/bigkarmashrine loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/deviggy loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/extendedgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/fan loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/gatecustomization loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/littleplanet loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/littleplanetring loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/shortcutcannon loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/spiketip loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/symbol_pearlchain loading as single image
[Debug  : RegionKit] assets/regionkit/sprites/tatgatesymbols loading as atlas
[Debug  : RegionKit] assets/regionkit/sprites/themast loading as atlas
[Debug  : RegionKit] Resources loaded in 00:00:00.0237450
[Debug  : RegionKit] Total load time 00:00:07.5685532
[Debug  : RegionKit] Load time for modules: 
[Debug  : RegionKit] 	Assets : 00:00:00.2326187
[Debug  : RegionKit] 	Common Hooks : 00:00:00.0310802
[Debug  : RegionKit] 	CustomSSSong : 00:00:00.0148048
[Debug  : RegionKit] 	RoomLoader : 00:00:00.0164666
[Debug  : RegionKit] 	The Mast : 00:00:00.4067736
[Debug  : RegionKit] 	Shelter Behaviors : 00:00:00.0080387
[Debug  : RegionKit] 	Shader Tools : 00:00:00.0165246
[Debug  : RegionKit] 	Room zones : 00:00:00.0004933
[Debug  : RegionKit] 	Room Slideshow : 00:00:00.0373175
[Debug  : RegionKit] 	Particles : 00:00:00.0032643
[Debug  : RegionKit] 	MiscObjects : 00:00:02.3495212
[Debug  : RegionKit] 	Multi-Color Snow : 00:00:00.2366765
[Debug  : RegionKit] 	Miscellanceous : 00:00:00.3961296
[Debug  : RegionKit] 	Machinery : 00:00:00.0216718
[Debug  : RegionKit] 	Insects : 00:00:00.2123830
[Debug  : RegionKit] 	IndividualPlacedObjectViewer : 00:00:00.0682986
[Debug  : RegionKit] 	Iggy : 00:00:00.0432891
[Debug  : RegionKit] 	GateCustomization : 00:00:00.2315659
[Debug  : RegionKit] 	Effects : 00:00:00.8917257
[Debug  : RegionKit] 	Echo Extender : 00:00:00.0779989
[Debug  : RegionKit] 	DevUI : 00:00:00.2478453
[Debug  : RegionKit] 	CustomProjections : 00:00:00.6725559
[Debug  : RegionKit] 	Concealed Garden : 00:00:00.1996896
[Debug  : RegionKit] 	Climbables : 00:00:00.0633141
[Debug  : RegionKit] 	BackgroundBuilder : 00:00:01.0303628
[Debug  : RegionKit] 	AridBarrens : 00:00:00.0002700
[Debug  : RegionKit] 	Animated Decals : 00:00:00.0572509
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\chaos.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\jet.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\lizcar.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\mist.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\scenes\ghost_jet.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\scenes\ghost_lizcar.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\scenes\scenechaos.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\scenes\scenelbtmy.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\scenes\slugcat_jet.json
[Debug  :  SlugBase] Added SlugBase object from c:\program files (x86)\steam\steamapps\common\rain world\rainworld_data\streamingassets\mods\the mist au (alpha)\slugbase\scenes\slugcat_lizcar.json
[Info   :RainMeadow] 00:01:38|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::GrabUpdate in 0.0014225s
[Info   :RainMeadow] 00:01:38|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 2 Jolly CoOP checks in Player::CanIPickThisUp in 0.0003356s
[Info   :RainMeadow] 00:01:38|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::GrabUpdate in 0.0011867s
[Info   :RainMeadow] 00:01:39|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 7 Jolly CoOP checks in Player::Update in 0.0013672s
[Info   :RainMeadow] 00:01:39|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::ClassMechanicsArtificer in 0.0001451s
[Info   :RainMeadow] 00:01:39|1553|ArenaHooks.Player_Collide2:Gourm Stomp RPC set with Player
[Info   :RainMeadow] 00:01:39|1553|ArenaHooks.Player_Collide2:Gourm Stomp RPC set with Player
[Info   :RainMeadow] 00:01:39|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::Collide in 0.0001308s
[Info   :RainMeadow] 00:01:39|1553|RainMeadow.PlayerHooks.GourmandOnBackMechanics:Overriden 1 comparisons in Player::SlugSlamConditions
[Info   :RainMeadow] 00:01:39|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Player::SlugSlamConditions in 0.0003646s
[Info   :RainMeadow] 00:01:40|1553|RainMeadow.JollyHooks.SoftDisableJollyCoOP:Replace 1 Jolly CoOP checks in Ghost::Update in 0.00033s
[Info   :RainMeadow] INIT SOUND LOADER
[Info   :RainMeadow] C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\mergedmods\soundeffects\sounds.txt
[Info   :RainMeadow] @M@ ExpeditionCoreFile OnFileMounted SUCCESS :: -179758848
[Info   :RainMeadow] @M@ CORE FILE IS BEING CHANGED! :: -179758848
[Info   :RainMeadow] Loading sounds
[Info   :RainMeadow] ------- Initiating import of 152 samples
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] !! Synced load state
[Info   :RainMeadow] Init Custom Templates
[Info   :RainMeadow] All clips loaded and assigned!
[Info   :RainMeadow] 00:01:49|3586|MatchmakingManager.JoinLobbyUsingCode:Attempting to join lobby with code: C:\Program Files (x86)\Steam\steamapps\common\Rain World\RainWorld.exe
[Info   :RainMeadow] 00:01:49|3586|MatchmakingManager.JoinLobbyUsingCode:No lobby found in that code.
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] 00:01:59|11348|SteamMatchmakingManager.LeaveLobby
[Info   :RainMeadow] 00:01:59|11348|RainMeadowModManager.Reset:Restoring config settings
[Info   :RainMeadow] 00:01:59|11348|SteamMatchmakingManager.initializeMePlayer
[Warning:Dress My Slugcat] DressMySlugcat: Duplicate spritesheet ID! "dressmyslugcat.cutscenescug"!

[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] !! Synced load state
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEMARK
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEMARK
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEMARK
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEMARK
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEMARK
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEGLOW
[Warning:RainMeadow] Savestate miner couldn't find: >RIPPLELEVEL<dpB>
[Warning:RainMeadow] Savestate miner couldn't find: >HASTHEMARK
[Warning:RainMeadow] Savestate miner couldn't find: >REDEXTRACYCLES
[Warning:RainMeadow] Savestate miner couldn't find: >ASCENDED
[Warning:RainMeadow] Savestate miner couldn't find: >HASROBO
[Warning:RainMeadow] Savestate miner couldn't find: >ALTENDING
[Warning:RainMeadow] Savestate miner couldn't find: >ENERGYRAILOFF
[Warning:RainMeadow] Savestate miner couldn't find: >MOONROBE
[Info   :RainMeadow] Checksum CORRECT!
[Warning:RainMeadow] WWW file FAILED: file:///C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\scenes\jetscenes\grass 3.png error: HTTP/1.1 404 Not Found
[Warning:RainMeadow] Error loading file: System.IO.FileLoadException: WWW loading error: HTTP/1.1 404 Not Found with file: file:///C:/Program Files (x86)/Steam/steamapps/common/Rain World/RainWorld_Data/StreamingAssets\scenes\jetscenes\grass 3.png
  at AssetManager.SafeWWWLoadTexture (UnityEngine.Texture2D& texture2D, System.String path, System.Boolean clampWrapMode, System.Boolean crispPixels) [0x0005a] in <0d3d3bd248b74a708fd84538ba1e82f5>:0 
  at (wrapper dynamic-method) Menu.MenuIllustration.DMD<Menu.MenuIllustration::LoadFile>(Menu.MenuIllustration,string)
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Drainworld flood set to 0
[Info   : RegionKit] [Echo Extender] Loading Echoes from Region Mods...
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Loading death persistent data
[Info   :RainMeadow] loaded 1 session records
[Info   :RainMeadow] Dynamic difficulty updated
[Info   :RainMeadow] Regions survived 1
[Info   :RainMeadow] Regions travelled 0
[Info   :RainMeadow] Clampings,  E: -7 H: 12
[Info   :RainMeadow] weighings,  E: -15 H: 10
[Info   :RainMeadow] POS 1
[Info   :RainMeadow] save deathPersistent data 1 sub karma: True (quit:True)
[Info   :RainMeadow] Saving death persistent data True True
[Info   :RainMeadow] WIN STATE SAVED (as death: True )
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Playerprog cansave is: Immediate
[Info   :RainMeadow] DYNAMIC DIFFICULTY: -0.6454294
[Warning:RainMeadow] Wrong syntax effect loaded: Flies
[Info   :RainMeadow] Chance value was: 27
[Info   :RainMeadow] Needed to be under 40
[Info   :RainMeadow] SUCCESS precycle length is 7451
[Info   :RainMeadow] First world loaded, holding precycle scale.
[Warning:RainMeadow] GATE_UW_SL less nodes than connections!
[Warning:RainMeadow] GATE_SL_MS less nodes than connections!
[Info   :RainMeadow] -- mapconfig as timeline: Rivulet
[Info   :RainMeadow] Creating region state for world: SL
[Info   :RainMeadow] Adapt world to region state SL
[Info   :RainMeadow] Region SL ticking forward 1 cycles. (+ 0 bonus cycles for bats and consumables)
[Info   :RainMeadow] Generate population for : SL FRESH: False
[Info   :RainMeadow] add NONE creature to respawns for lineage 7031
[Info   :RainMeadow] add NONE creature to respawns for lineage 7032
[Info   :RainMeadow] add NONE creature to respawns for lineage 7033
[Info   :RainMeadow] Mapped all 6 swarm rooms
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:   ##                         
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:   ##    ###                  
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor: .###%######             #####
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:#-##=.=####             ##### 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:#==#########         #######  
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:############       ########   
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:##########################    
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor: ########################     
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:  #####################       
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:   ##################         
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:  ################            
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:  ###    ###                  
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:   ##      ####               
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:   ###       ##               
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Checking files
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Printing all directories in soundeffects
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:The file exists actually
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:it has read all its lines
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Finga$4-3 4-5 4-7,3-4 3-5 3-6 2-1$Balaboo|Balaboo|Intermediary in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Balaboo$4-2 4-3 4-6,3-2 2-6 2-5$Triad|Midlowhigh2 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Heyolol$5-4 5-1 4-3,3-2 2-3$Finga|Auto in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Triad$4-1 4-3 4-5,3-1 3-3 2-5 2-6 2-4$Triad2|Finga|Uptwomore in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Triad2$4-1 4-3 4-5,3-1 3-3 2-5 2-6 2-4$Finga|Triad in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Auto$5-3 5-1 4-4,4-1 3-5$Uptwomore|TheFourthFinal in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: MidOlora$5-5 5-2 4-6,4-3 3-6 3-2$Midlowhigh2 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: MidOlora2$5-5 5-2 4-6,4-3 3-6 3-2$Susthing|Midlowhigh in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Susthing$5-5 5-4 5-1,4-5 4-4 3-6$Uptwomore in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Midlowhigh$5-3 4-7 4-5,4-2 3-5 3-4$Midlowhigh2|Midlowhigh2|Susthing2 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Uptwomore$5-5 5-3 4-6,4-4 4-3 3-6$MidOlora|TheFourthFinal in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: TheFourthFinal$5-3 4-7,4-5 4-1 3-5$TwoThree in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: TwoThree$5-3 5-1,4-6 4-3 3-6$Aeyyup|Aeyyup in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: SADNESS$4-6 4-4,4-1 3-6 3-2$Final in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Final$5-1 4-6 4-5,4-3 3-5 3-1$Actuallysadthough|Butheythere in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Actuallysadthough$4-7 4-3,4-1 3-6 3-2$butaftersadness|Midlowhigh2 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: NextHigh$5-6 5-4 5-2,4-4 4-1 3-5 3-2$Triad in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: butaftersadness$5-3 5-1 4-4,4-1 3-6 3-4$NextHigh in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Butheythere$5-5 5-2 5-1,4-4 4-2 3-6$MidOlora3 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: MidOlora3$5-5 5-2 4-6,4-3 3-6$Aeyyup in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Midlowhigh2$5-3 4-7 4-5,4-2 3-5 3-4$Midlowhigh|MidOlora2 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Susthing2$5-5 5-4 5-1,4-5 4-4 3-6$Uptwomore|Heyolol in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Aeyyup$5-4 5-1 4-6,4-4 3-6$SADNESS|Triad2 in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Intermediary$6-1 5-4 5-2,4-2 4-1 3-5 3-4$SADNESS in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Balaboofake$4-2 4-3 4-6,3-2 2-6 2-5$Fingafake in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Plopmachine:  Registered Entry: Fingafake$4-3 4-5 4-7,3-4 3-5 3-6 2-1$Balaboofake in 
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:it has added the thongs
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Yo it's done with sfx
[Info   :RainMeadow] 00:03:06|29400|PlopMachine.RainWorldGame_ctor:Created wetloop
[Error  : RegionKit] RainSong: Buddha is dead: System.NullReferenceException: Object reference not set to an instance of an object
  at RainSong.RainUpdatePatch (On.RainCycle+orig_Update orig, RainCycle self) [0x000de] in <5867de4cfc4d41c59f92a8a18486fb81>:0 
[Info   :RainMeadow] killed all hostiles in shelter
[Error  : RegionKit] RainSong: Buddha is dead: System.NullReferenceException: Object reference not set to an instance of an object
  at RainSong.RainUpdatePatch (On.RainCycle+orig_Update orig, RainCycle self) [0x000de] in <5867de4cfc4d41c59f92a8a18486fb81>:0 
[Error  : RegionKit] RainSong: Buddha is dead: System.NullReferenceException: Object reference not set to an instance of an object
  at RainSong.RainUpdatePatch (On.RainCycle+orig_Update orig, RainCycle self) [0x000de] in <5867de4cfc4d41c59f92a8a18486fb81>:0 
[Error  : RegionKit] RainSong: Buddha is dead: System.NullReferenceException: Object reference not set to an instance of an object
  at RainSong.RainUpdatePatch (On.RainCycle+orig_Update orig, RainCycle self) [0x000de] in <5867de4cfc4d41c59f92a8a18486fb81>:0 
[Info   :RainMeadow] realize critter in shelter Slugcat
[Info   :RainMeadow] Creating player graphics! 0
[Info   :RainMeadow] gate condition on map, karma 5
[Info   :RainMeadow] gate condition on map, karma 2
[Info   :RainMeadow] gate condition on map, karma 2
[Info   :RainMeadow] gate condition on map, karma 1
[Info   :RainMeadow] gate condition on map, karma R
[Info   :RainMeadow] LC/MS gate condition on map False
[Info   :RainMeadow] gate condition on map, karma 5
[Info   :RainMeadow] gate condition on map, karma 3
[Info   :RainMeadow] Map load request for: SL
[Info   :RainMeadow] Checksum CORRECT!
[Info   :RainMeadow] Loading map bytes: SL
[Info   :RainMeadow] NEW MUSIC REGION: SL
[Info   :RainMeadow] Spawning saved objects! pending objects count is: 0
[Info   :RainMeadow] Spawning saved friends! pending friends count is: 0
[Info   :RainMeadow] Spawncritters running!
[Info   :RainMeadow] Procedural music day trigger
[Info   :RainMeadow] 00:03:22|38901|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:7
[Info   :RainMeadow] 00:03:24|40975|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:5
[Info   :RainMeadow] lizard idle spot in den. Setting to critter pos
[Info   :RainMeadow] lizard idle spot in den. Setting to critter pos
[Info   :RainMeadow] 00:03:27|43710|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:7
[Info   :RainMeadow] 00:03:29|45746|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:7
[Info   :RainMeadow] 00:03:32|48977|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:1
[Info   :RainMeadow] 00:03:33|50366|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Tentacle Plant Random movement for 0 ticks.
[Info   :RainMeadow] Aquapede Random movement for 223 ticks.
[Info   :RainMeadow] client add to mapper: 2
[Info   :RainMeadow] client add to mapper: 3
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] 00:03:37|54645|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] Reactivate scrambling, new sub region!
[Info   :RainMeadow] Eel ID.7050.6697 searching for new den using coordinate r:553 n:0
[Warning:RainMeadow] Eel ID.7050.6697 found no den, stranded!
[Info   :RainMeadow] 00:03:41|58166|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:03:42|59444|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] CONSUMED: DangleFruit
[Info   :RainMeadow] Item consumed. Flower: False
[Info   :RainMeadow] 00:03:46|63415|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] 00:03:47|64395|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] Resetting Spear to outside terrain
[Info   :RainMeadow] 00:03:49|66264|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:7
[Info   :RainMeadow] 00:03:50|67175|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:3
[Info   :RainMeadow] 00:03:52|69642|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:03:53|70100|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:7
[Info   :RainMeadow] 00:03:54|71370|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] 00:03:55|71974|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 1, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] 00:03:55|71974|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] Eel ID.7050.6697 searching for new den using coordinate r:553 n:0
[Info   :RainMeadow] Eel found its original spawn den, resetting den position!
[Info   :RainMeadow] From: null To: WC ~ r: 553 x: -1 y: -1 n:5
[Info   :RainMeadow] 00:03:59|76637|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] 00:04:01|78345|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:1
[Info   :RainMeadow] 00:04:02|78943|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] acc map handover Jet Fish ID.7061.6704 frm Jet Fish ID.7056.6700
[Info   :RainMeadow] CONSUMED: DangleFruit
[Info   :RainMeadow] Item consumed. Flower: False
[Info   :RainMeadow] 00:04:08|85314|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] 00:04:09|86444|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] Die! Sea Leech
[Info   :RainMeadow] 00:04:16|92895|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 2, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] 00:04:16|92895|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 1, WC ~ r: 529 x: -1 y: -1 n:5
[Info   :RainMeadow] 00:04:16|92895|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] Die! Sea Leech
[Info   :RainMeadow] 00:04:20|96943|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:04:20|97325|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] 00:04:22|99269|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:9
[Info   :RainMeadow] Die! Jet Fish
[Info   :RainMeadow] CRITTER TO RESPAWN : Jet Fish ID.7054.6698
[Info   :RainMeadow] not killed by player - immediate respawn
[Info   :RainMeadow] Die! Sea Leech
[Info   :RainMeadow] 00:04:25|102044|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 552 x: -1 y: -1 n:2
[Info   :RainMeadow] White Lizard ID.7027.6676 searching for new den using coordinate r:552 n:0
[Warning:RainMeadow] White Lizard ID.7027.6676 found no den, stranded!
[Info   :RainMeadow] 00:04:27|104515|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] White Lizard ID.7027.6676 searching for new den using coordinate r:552 n:0
[Info   :RainMeadow] White Lizard found its original spawn den, resetting den position!
[Info   :RainMeadow] From: null To: WC ~ r: 549 x: -1 y: -1 n:2
[Info   :RainMeadow] 00:04:30|106842|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:7
[Info   :RainMeadow] Die! Sea Leech
[Info   :RainMeadow] Die! Jet Fish
[Info   :RainMeadow] CRITTER TO RESPAWN : Jet Fish ID.7061.6704
[Info   :RainMeadow] not killed by player - immediate respawn
[Info   :RainMeadow] 00:04:33|109846|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:04:39|116145|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:04:48|125597|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:04:50|127174|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 529 x: -1 y: -1 n:15
[Info   :RainMeadow] Big Eel ENTER ROOM FROM BORDER
[Info   :RainMeadow] 00:04:51|127770|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] CONSUMED: DangleFruit
[Info   :RainMeadow] Item consumed. Flower: False
[Info   :RainMeadow] 00:04:58|134969|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] Die! Jet Fish
[Info   :RainMeadow] CRITTER TO RESPAWN : Jet Fish ID.7056.6700
[Info   :RainMeadow] not killed by player - immediate respawn
[Info   :RainMeadow] Die! Sea Leech
[Info   :RainMeadow] 00:05:44|180710|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 555 x: -1 y: -1 n:1
[Info   :RainMeadow] 00:05:46|183038|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Tentacle Plant Random movement for 0 ticks.
[Info   :RainMeadow] Jet Fish Random movement for 500 ticks.
[Info   :RainMeadow] Jet Fish Random movement for 500 ticks.
[Info   :RainMeadow] 00:05:51|187692|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] CONSUMED: DangleFruit
[Info   :RainMeadow] Item consumed. Flower: False
[Info   :RainMeadow] 00:05:57|193846|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 569 x: -1 y: -1 n:0
[Info   :RainMeadow] kill distant room: SL_S08
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Aquapede Random movement for 500 ticks.
[Info   :RainMeadow] Jet Fish Random movement for 500 ticks.
[Info   :RainMeadow] 00:06:03|199692|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] 00:06:04|201113|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 555 x: -1 y: -1 n:2
[Info   :RainMeadow] 00:06:05|201787|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Tentacle Plant Random movement for 0 ticks.
[Info   :RainMeadow] Jet Fish Random movement for 500 ticks.
[Info   :RainMeadow] 00:06:17|213821|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 569 x: -1 y: -1 n:1
[Info   :RainMeadow] Jet Fish ID.7029.6678 searching for new den using coordinate r:569 n:2
[Info   :RainMeadow] Jet Fish ID.7029.6678 found a den!
[Info   :RainMeadow] den WC ~ r: 569 x: -1 y: -1 n:2
[Info   :RainMeadow] 00:06:19|215770|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] Jet Fish found its original spawn den, resetting den position!
[Info   :RainMeadow] From: WC ~ r: 569 x: -1 y: -1 n:2 To: WC ~ r: 570 x: -1 y: -1 n:4
[Info   :RainMeadow] CONSUMED: DangleFruit
[Info   :RainMeadow] Item consumed. Flower: False
[Info   :RainMeadow] 00:06:26|222746|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] CONSUMED: DangleFruit
[Info   :RainMeadow] Item consumed. Flower: False
[Info   :RainMeadow] 00:06:28|225445|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 569 x: -1 y: -1 n:0
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Aquapede Random movement for 334 ticks.
[Info   :RainMeadow] 00:06:33|230292|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 555 x: -1 y: -1 n:2
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Tentacle Plant Random movement for 0 ticks.
[Info   :RainMeadow] 00:06:40|237142|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] 00:07:03|259740|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 530 x: -1 y: -1 n:14
[Info   :RainMeadow] Big Eel ENTER ROOM FROM BORDER
[Info   :RainMeadow] 00:07:03|260565|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] 00:07:08|265287|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:8
[Info   :RainMeadow] 00:07:13|269865|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:6
[Info   :RainMeadow] 00:07:18|275491|RainMeadow.ShortcutHooks.ShortcutHandler_Update:ShortcutHandler, 0, WC ~ r: 553 x: -1 y: -1 n:3
[Info   :RainMeadow] Activated random neighbor
[Info   :RainMeadow] Activated random neighbor
[Debug  :Push to Meow] Using normal meow type for Jet because they don't have a custom meow registered
[Debug  :Push to Meow] Using normal meow type for Jet because they don't have a custom meow registered
[Debug  :Push to Meow] Using normal meow type for Jet because they don't have a custom meow registered
